
// src/services/supabaseClient.ts
import { createClient } from '@supabase/supabase-js';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from '../config';

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  throw new Error(
    "Supabase URL ou Anon Key não estão definidas no arquivo src/config.ts. " +
    "Por favor, crie o arquivo src/config.ts, adicione suas credenciais do Supabase, " +
    "e certifique-se de adicioná-lo ao .gitignore."
  );
}

export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
