
import { NotaFiscal, ProdutoMaster, Discrepancy, DocumentType, ReportData, SalesEvolutionDataPoint } from '../types';

// --- Tax Calculation ---
export const calculateTotalTaxesByType = (notes: NotaFiscal[]): Record<string, number> => {
  const totals: Record<string, number> = {
    ICMS: 0,
    ICMSST: 0,
    IPI: 0,
    PIS: 0,
    COFINS: 0,
    ISSQN: 0,
    Outros: 0, // For vTotTrib if not broken down
  };

  notes.forEach(note => {
    if (note.error) return;
    note.itens.forEach(item => {
      totals.ICMS += parseFloat(item.ICMS?.v || '0');
      totals.ICMSST += parseFloat(item.ICMSST?.v || '0') + parseFloat(item.ICMSST?.vICMSSTRet || '0') + parseFloat(item.ICMSST?.vICMSSTDest || '0');
      totals.IPI += parseFloat(item.IPI?.v || '0');
      totals.PIS += parseFloat(item.PIS?.v || '0') + parseFloat(item.PISST?.v || '0');
      totals.COFINS += parseFloat(item.COFINS?.v || '0') + parseFloat(item.COFINSST?.v || '0');
      totals.ISSQN += parseFloat(item.ISSQN?.v || '0');
    });
    // Add vTotTrib if it exists and items don't sum up (or for simpler overview)
    // This logic might need refinement based on how vTotTrib is populated and used.
    // For now, if detailed items are present, they are preferred.
    // If no items or items have no tax info, vTotTrib could be an "Outros" category.
    const itemTaxesSum = totals.ICMS + totals.ICMSST + totals.IPI + totals.PIS + totals.COFINS + totals.ISSQN;
    if (parseFloat(note.vTotTrib || '0') > 0 && itemTaxesSum === 0) {
        totals.Outros += parseFloat(note.vTotTrib || '0');
    }
  });
  return totals;
};


// --- Fiscal Monitoring ---
export const performFiscalMonitoring = (notes: NotaFiscal[], productsMaster: ProdutoMaster[]): Discrepancy[] => {
  const discrepancies: Discrepancy[] = [];
  if (!productsMaster || productsMaster.length === 0) return discrepancies;

  const masterMap = new Map(productsMaster.map(p => [p.CODIGO_INTERNO_PRODUTO, p]));

  notes.forEach(note => {
    if (note.error || note.documentType === 'UNKNOWN' || note.documentType === 'NFSe') return; // Skip NFSe for now due to variability

    note.itens.forEach(item => {
      const masterProduct = masterMap.get(item.cProd);
      if (masterProduct) {
        // NCM
        if (item.NCM && masterProduct.NCM && item.NCM !== masterProduct.NCM) {
          discrepancies.push({
            noteId: note.id, noteFileName: note.fileName, itemCode: item.cProd, itemDescription: item.xProd,
            field: 'NCM', valueInNote: item.NCM, valueInMaster: masterProduct.NCM,
            message: `NCM na nota (${item.NCM}) difere do cadastro (${masterProduct.NCM}).`
          });
        }
        // CST ICMS (Example, more specific tax fields can be added)
        const itemCstIcms = item.ICMS?.CST || item.ICMS?.CSOSN;
        if (itemCstIcms && masterProduct.CST_CSOSN_ICMS_PADRAO && itemCstIcms !== masterProduct.CST_CSOSN_ICMS_PADRAO) {
           discrepancies.push({
            noteId: note.id, noteFileName: note.fileName, itemCode: item.cProd, itemDescription: item.xProd,
            field: 'CST/CSOSN ICMS', valueInNote: itemCstIcms, valueInMaster: masterProduct.CST_CSOSN_ICMS_PADRAO,
            message: `CST/CSOSN ICMS na nota (${itemCstIcms}) difere do padrão (${masterProduct.CST_CSOSN_ICMS_PADRAO}).`
          });
        }
        // Aliquota ICMS
        if (item.ICMS?.p && masterProduct.ALIQUOTA_ICMS_PADRAO && parseFloat(item.ICMS.p) !== parseFloat(masterProduct.ALIQUOTA_ICMS_PADRAO)) {
             discrepancies.push({
                noteId: note.id, noteFileName: note.fileName, itemCode: item.cProd, itemDescription: item.xProd,
                field: 'Alíquota ICMS', valueInNote: item.ICMS.p, valueInMaster: masterProduct.ALIQUOTA_ICMS_PADRAO,
                message: `Alíquota ICMS na nota (${item.ICMS.p}%) difere do padrão (${masterProduct.ALIQUOTA_ICMS_PADRAO}%).`
            });
        }
      }
    });
  });
  return discrepancies;
};

// --- Sales/Purchase Reports ---
export const getSalesByProductReport = (notes: NotaFiscal[]): ReportData => {
  const sales: Record<string, { code: string; description: string; quantity: number; totalValue: number }> = {};
  notes.filter(n => n.tipo === 'SAIDA' && !n.error).forEach(note => {
    note.itens.forEach(item => {
      if (!sales[item.cProd]) {
        sales[item.cProd] = { code: item.cProd, description: item.xProd, quantity: 0, totalValue: 0 };
      }
      sales[item.cProd].quantity += parseFloat(item.qCom || '0');
      sales[item.cProd].totalValue += parseFloat(item.vProd || '0');
    });
  });
  return {
    title: "Vendas por Produto",
    data: Object.values(sales).sort((a,b) => b.totalValue - a.totalValue),
    columns: [
        { key: 'description', label: 'Produto' },
        { key: 'quantity', label: 'Quantidade Vendida' },
        { key: 'totalValue', label: 'Valor Total Vendido (R$)' },
    ],
    summary: {
        "Total de Produtos Vendidos": Object.keys(sales).length,
        "Valor Total Geral de Vendas": Object.values(sales).reduce((sum, p) => sum + p.totalValue, 0).toFixed(2)
    }
  };
};

export const getPurchasesByProductReport = (notes: NotaFiscal[]): ReportData => {
    const purchases: Record<string, { code: string; description: string; quantity: number; totalValue: number }> = {};
    notes.filter(n => n.tipo === 'ENTRADA' && !n.error).forEach(note => {
        note.itens.forEach(item => {
        if (!purchases[item.cProd]) {
            purchases[item.cProd] = { code: item.cProd, description: item.xProd, quantity: 0, totalValue: 0 };
        }
        purchases[item.cProd].quantity += parseFloat(item.qCom || '0');
        purchases[item.cProd].totalValue += parseFloat(item.vProd || '0');
        });
    });
    return {
        title: "Compras por Produto",
        data: Object.values(purchases).sort((a,b) => b.totalValue - a.totalValue),
        columns: [
            { key: 'description', label: 'Produto' },
            { key: 'quantity', label: 'Quantidade Comprada' },
            { key: 'totalValue', label: 'Valor Total Comprado (R$)' },
        ],
        summary: {
            "Total de Produtos Comprados": Object.keys(purchases).length,
            "Valor Total Geral de Compras": Object.values(purchases).reduce((sum, p) => sum + p.totalValue, 0).toFixed(2)
        }
    };
};

export const getSalesEvolutionData = (notes: NotaFiscal[]): SalesEvolutionDataPoint[] => {
  const salesByPeriod: Record<string, number> = {};

  notes.forEach(note => {
    if (note.tipo === 'SAIDA' && !note.error && note.dhEmi && note.vNF) {
      try {
        const date = new Date(note.dhEmi);
        // Check if date is valid before proceeding
        if (isNaN(date.getTime())) {
            console.warn(`Invalid date format for note ${note.id}: ${note.dhEmi}`);
            return; // Skip this note
        }
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0'); // JavaScript months are 0-indexed
        const period = `${year}-${month}`;
        
        salesByPeriod[period] = (salesByPeriod[period] || 0) + parseFloat(note.vNF);
      } catch (e) {
        console.warn(`Error processing date for note ${note.id}: ${note.dhEmi}`, e);
      }
    }
  });

  return Object.entries(salesByPeriod)
    .map(([period, totalSales]) => ({ period, totalSales }))
    .sort((a, b) => a.period.localeCompare(b.period)); // Sort by period
};

export const getGeneralSummary = (notes: NotaFiscal[], products: ProdutoMaster[]) => {
    const totalNotes = notes.length;
    const salesNotes = notes.filter(n => n.tipo === 'SAIDA' && !n.error);
    const purchaseNotes = notes.filter(n => n.tipo === 'ENTRADA' && !n.error);

    const totalSalesValue = salesNotes.reduce((sum, n) => sum + parseFloat(n.vNF || '0'), 0);
    const totalPurchaseValue = purchaseNotes.reduce((sum, n) => sum + parseFloat(n.vNF || '0'), 0);
    
    const taxes = calculateTotalTaxesByType(notes);
    const totalTaxSum = Object.values(taxes).reduce((sum, val) => sum + val, 0);

    const salesByProduct = getSalesByProductReport(notes).data;
    const topSoldProduct = salesByProduct.length > 0 ? salesByProduct[0] : null;

    const classifiedSalesCount = salesNotes.length;
    const classifiedPurchasesCount = purchaseNotes.length;
    
    const salesEvolution = getSalesEvolutionData(notes);

    return {
        totalNotes,
        totalProducts: products.length,
        totalSalesValue: totalSalesValue.toFixed(2),
        totalPurchaseValue: totalPurchaseValue.toFixed(2),
        totalTaxSum: totalTaxSum.toFixed(2),
        taxesBreakdown: Object.fromEntries(Object.entries(taxes).map(([key, value]) => [key, value.toFixed(2)])),
        topSoldProductName: topSoldProduct?.description,
        topSoldProductValue: topSoldProduct?.totalValue.toFixed(2),
        classifiedSalesCount,
        classifiedPurchasesCount,
        salesEvolutionData: salesEvolution, // Added sales evolution data
    };
};
