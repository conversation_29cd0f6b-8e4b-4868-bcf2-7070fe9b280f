// src/config.ts

// IMPORTANTE:
// 1. <PERSON><PERSON><PERSON><PERSON> COM SUA URL REAL DO SUPABASE E CHAVE ANON ABAIXO SE ESTIVEREM VAZIAS.
// 2. SE VOCÊ COLOCAR CHAVES SECRETAS (COMO GEMINI_API_KEY) DIRETAMENTE NESTE ARQUIVO,
//    ADICIONE ESTE ARQUIVO AO SEU .gitignore PARA EVITAR COMMITAR SUAS CREDENCIAIS.
//    A MELHOR PRÁTICA PARA CHAVES SECRETAS É USAR VARIÁVEIS DE AMBIENTE NO BUILD/DEPLOY.

export const SUPABASE_URL: string | undefined = 'https://kyfyedrkmmwpzuidmshm.supabase.co';
export const SUPABASE_ANON_KEY: string | undefined = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt5ZnllZHJrbW13cHp1aWRtc2htIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1Mjg1MjYsImV4cCI6MjA2NDEwNDUyNn0.KiabnaIeeoeOheWaHrJpc2yt9uq4prcQVfkOXQbpU5s';

// Chave da API Gemini.
// ATENÇÃO: É ALTAMENTE RECOMENDADO NÃO COLOCAR CHAVES DE API SECRETAS DIRETAMENTE
// NO CÓDIGO DO FRONTEND. CONSIDERE USAR VARIÁVEIS DE AMBIENTE (EX: VITE_GEMINI_API_KEY)
// OU, MELHOR AINDA, FAZER AS CHAMADAS À API GEMINI ATRAVÉS DE UM BACKEND (EX: SUPABASE EDGE FUNCTIONS).
export const GEMINI_API_KEY: string | undefined = undefined;

// Verificação se variáveis críticas estão definidas (feedback para desenvolvimento local)
if (typeof SUPABASE_URL === 'undefined' || SUPABASE_URL === '' ||
    typeof SUPABASE_ANON_KEY === 'undefined' || SUPABASE_ANON_KEY === '') {
  const supabaseWarning = "Supabase URL ou Anon Key não estão definidas ou estão vazias no arquivo src/config.ts. " +
    "Verifique o arquivo src/config.ts e adicione suas credenciais do Supabase. " +
    "Se este arquivo estiver no .gitignore e você estiver em um ambiente de deploy, " +
    "certifique-se de que as variáveis de ambiente correspondentes (ex: VITE_SUPABASE_URL) estão configuradas.";
  console.warn(supabaseWarning);
  // Você pode querer lançar um erro ou ter um comportamento mais forte aqui se elas forem absolutamente necessárias para o app carregar
}

// Verificação opcional para GEMINI_API_KEY, considerando que pode vir de process.env no geminiService.
// Esta verificação em config.ts é mais para o caso de você depender exclusivamente dela aqui.
if (typeof GEMINI_API_KEY === 'undefined' || GEMINI_API_KEY === '') {
  // Verifica se a chave também não está vindo de uma variável de ambiente típica do frontend (exemplo com VITE)
  // Adapte `import.meta.env.VITE_GEMINI_API_KEY` para o seu bundler se for diferente (ex: `process.env.REACT_APP_GEMINI_API_KEY`)
  const envApiKey = typeof import.meta !== 'undefined' && import.meta.env ? import.meta.env.VITE_GEMINI_API_KEY : undefined;

  if (!envApiKey) { // Só mostra o aviso se não houver fallback via variável de ambiente no frontend
    const geminiWarning = "GEMINI_API_KEY não está definida ou está vazia no arquivo src/config.ts E NENHUM FALLBACK DE VARIÁVEL DE AMBIENTE FOI DETECTADO. " +
      "As funcionalidades de IA podem não funcionar. " +
      "Considere usar variáveis de ambiente (ex: VITE_GEMINI_API_KEY) para chaves secretas.";
    console.warn(geminiWarning);
  }
}