
import React, { useState, useRef, useEffect } from 'react';
import { ChatMessage } from '../types';
import { PaperAirplaneIcon, UserCircleIcon, SparklesIcon, LoadingSpinner } from './Icons';
import { INITIAL_CHAT_MESSAGE } from '../constants';

interface AiChatProps {
  messages: ChatMessage[];
  onSendMessage: (message: string) => Promise<void>;
  isLoading: boolean;
}

const AiChat: React.FC<AiChatProps> = ({ messages: propMessages, onSendMessage, isLoading }) => {
  const [inputMessage, setInputMessage] = useState('');
  const chatContainerRef = useRef<HTMLDivElement>(null);

  const allMessages = propMessages.length > 0 ? propMessages : [INITIAL_CHAT_MESSAGE];

  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [allMessages]);

  const handleSend = async () => {
    if (inputMessage.trim() === '' || isLoading) return;
    setInputMessage('');
    await onSendMessage(inputMessage.trim());
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="flex flex-col h-[calc(100vh-10rem)] bg-white shadow-lg rounded-lg">
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-xl font-semibold text-gray-800 flex items-center">
          <SparklesIcon className="w-6 h-6 mr-2 text-indigo-500" />
          Assistente Fiscal Inteligente
        </h3>
        <p className="text-xs text-gray-500">Faça perguntas sobre seus dados fiscais.</p>
      </div>

      <div ref={chatContainerRef} className="flex-1 p-6 space-y-4 overflow-y-auto">
        {allMessages.map((msg: ChatMessage) => (
          <div key={msg.id} className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div className={`flex items-end max-w-xl space-x-2`}>
              {msg.sender === 'ai' && <SparklesIcon className="w-6 h-6 text-indigo-500 mb-1 flex-shrink-0" />}
              <div
                className={`px-4 py-3 rounded-xl shadow ${
                  msg.sender === 'user'
                    ? 'bg-indigo-600 text-white rounded-br-none'
                    : 'bg-gray-200 text-gray-800 rounded-bl-none'
                }`}
              >
                <p className="text-sm whitespace-pre-wrap">{msg.text}</p>
                <p className={`text-xs mt-1 ${msg.sender === 'user' ? 'text-indigo-200' : 'text-gray-500'} text-right`}>
                  {new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </p>
              </div>
              {msg.sender === 'user' && <UserCircleIcon className="w-6 h-6 text-gray-400 mb-1 flex-shrink-0" />}
            </div>
          </div>
        ))}
        {isLoading && allMessages[allMessages.length -1]?.sender === 'user' && (
           <div className="flex justify-start">
             <div className="flex items-end max-w-xl space-x-2">
                <SparklesIcon className="w-6 h-6 text-indigo-500 mb-1" />
                <div className="px-4 py-3 rounded-xl shadow bg-gray-200 text-gray-800 rounded-bl-none">
                    <LoadingSpinner className="w-5 h-5 text-indigo-500"/>
                </div>
             </div>
           </div>
        )}
{/* SUGESTÕES PROATIVAS */}
        {(() => {
          // Busca a última mensagem da IA
          const lastAiMsg = [...allMessages].reverse().find(m => m.sender === 'ai');
          if (!lastAiMsg || !lastAiMsg.text.includes('Sugestões de perguntas:')) return null;

          // Extrai sugestões após o marcador
          const parts = lastAiMsg.text.split('Sugestões de perguntas:');
          if (parts.length < 2) return null;
          // Divide por linhas, remove vazios e caracteres especiais
          const suggestions = parts[1]
            .split('\n')
            .map((s: string) => s.replace(/^[-*•\d. ]+/, '').trim())
            .filter((s: string) => s.length > 0 && s.length < 120);

          if (suggestions.length === 0) return null;

          return (
            <div className="mb-4 flex flex-wrap gap-2">
              {suggestions.map((s: string, i: number) => (
                <button
                  key={i}
                  onClick={() => !isLoading && onSendMessage(s)}
                  className="px-3 py-1 bg-indigo-100 text-indigo-700 rounded-full text-xs hover:bg-indigo-200 transition disabled:opacity-50"
                  disabled={isLoading}
                  aria-label={`Enviar sugestão: ${s}`}
                >
                  {s}
                </button>
              ))}
            </div>
          );
        })()}
      </div>

      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center space-x-3">
          <input
            type="text"
            value={inputMessage}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Digite sua pergunta aqui..."
            className="flex-1 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-shadow"
            disabled={isLoading}
          />
          <button
            onClick={handleSend}
            disabled={isLoading || inputMessage.trim() === ''}
            className="p-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:bg-indigo-300 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
            aria-label="Enviar mensagem"
          >
            {isLoading ? <LoadingSpinner className="w-6 h-6"/> : <PaperAirplaneIcon className="w-6 h-6" />}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AiChat;
