
import { ProdutoMaster, ChatMessage } from './types';

export const PRODUCT_MASTER_COLUMNS: Array<keyof ProdutoMaster> = [
  'CODIGO_INTERNO_PRODUTO',
  'DESCRICAO_PRODUTO',
  'EAN_GTIN',
  'NCM',
  'CEST',
  'ORIGEM_MERCADORIA',
  'UNIDADE_MEDIDA_COMERCIAL',
  'ALIQUOTA_ICMS_PADRAO',
  'CST_CSOSN_ICMS_PADRAO',
  'ALIQUOTA_IPI_PADRAO',
  'CST_IPI_PADRAO',
  'ALIQUOTA_PIS_PADRAO',
  'CST_PIS_PADRAO',
  'ALIQUOTA_COFINS_PADRAO',
  'CST_COFINS_PADRAO',
  'REGIME_TRIBUTARIO_PRODUTO',
  'CATEGORIA_PRODUTO',
  'MARCA_PRODUTO',
  'FORNECEDOR_PRINCIPAL_CNPJ_CPF',
  'PRECO_CUSTO_ULTIMA_ENTRADA',
  'PRECO_VENDA_PADRAO',
  'STATUS_PRODUTO',
  'DATA_ULTIMA_ATUALIZACAO_CADASTRO',
];

export const PRODUCT_MASTER_COLUMN_DESCRIPTIONS: Record<keyof ProdutoMaster, { desc: string, example: string, required: boolean }> = {
  CODIGO_INTERNO_PRODUTO: { desc: "Código único interno do produto (SKU).", example: "PROD-00123", required: true },
  DESCRICAO_PRODUTO: { desc: "Nome/descrição completa e padronizada do produto.", example: "REFRIGERANTE COLA 2L", required: true },
  EAN_GTIN: { desc: "Código de barras do produto (EAN/GTIN).", example: "7890000123456", required: false },
  NCM: { desc: "Nomenclatura Comum do Mercosul.", example: "22021000", required: true },
  CEST: { desc: "Código Especificador da Substituição Tributária.", example: "0300100", required: false },
  ORIGEM_MERCADORIA: { desc: "Código da origem da mercadoria (0-Nacional, etc.).", example: "0", required: true },
  UNIDADE_MEDIDA_COMERCIAL: { desc: "Unidade de medida de comercialização (UN, CX, KG, etc.).", example: "UN", required: true },
  ALIQUOTA_ICMS_PADRAO: { desc: "Alíquota padrão de ICMS para vendas internas (%).", example: "18", required: true },
  CST_CSOSN_ICMS_PADRAO: { desc: "CST ou CSOSN padrão para o ICMS.", example: "102", required: true },
  ALIQUOTA_IPI_PADRAO: { desc: "Alíquota padrão de IPI (%), se aplicável.", example: "5", required: false },
  CST_IPI_PADRAO: { desc: "CST do IPI padrão, se aplicável.", example: "50", required: false },
  ALIQUOTA_PIS_PADRAO: { desc: "Alíquota padrão de PIS (%).", example: "1.65", required: true },
  CST_PIS_PADRAO: { desc: "CST do PIS padrão.", example: "01", required: true },
  ALIQUOTA_COFINS_PADRAO: { desc: "Alíquota padrão de COFINS (%).", example: "7.6", required: true },
  CST_COFINS_PADRAO: { desc: "CST da COFINS padrão.", example: "01", required: true },
  REGIME_TRIBUTARIO_PRODUTO: { desc: "Descrição do regime tributário (Ex: Tributado, S.T.).", example: "Tributado", required: false },
  CATEGORIA_PRODUTO: { desc: "Categoria interna para organização.", example: "Bebidas", required: false },
  MARCA_PRODUTO: { desc: "Marca do produto.", example: "Marca Exemplo", required: false },
  FORNECEDOR_PRINCIPAL_CNPJ_CPF: { desc: "CNPJ/CPF do principal fornecedor.", example: "12345678000199", required: false },
  PRECO_CUSTO_ULTIMA_ENTRADA: { desc: "Preço de custo da última aquisição/custo médio.", example: "3.50", required: false },
  PRECO_VENDA_PADRAO: { desc: "Preço de venda padrão do produto.", example: "5.99", required: false },
  STATUS_PRODUTO: { desc: "Situação do produto (Ativo, Inativo).", example: "Ativo", required: false },
  DATA_ULTIMA_ATUALIZACAO_CADASTRO: { desc: "Data da última alteração no cadastro.", example: "29/05/2025", required: false },
};

export const GEMINI_TEXT_MODEL = 'gemini-2.5-flash-preview-04-17';

export const INITIAL_CHAT_MESSAGE: ChatMessage = {
  id: 'initial-ai-message',
  sender: 'ai',
  text: 'Olá! Sou seu assistente fiscal inteligente. Como posso ajudar você hoje com seus dados fiscais?',
  timestamp: new Date(),
};
