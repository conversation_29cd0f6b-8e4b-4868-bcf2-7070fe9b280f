
import { NotaFiscal, ProdutoNotaFiscal, TaxDetail, DocumentType } from '../types';

const NFE_NS = "http://www.portalfiscal.inf.br/nfe";

// Helper to get text content of a namespaced element
const getText = (element: Element | undefined | null, tagName: string, ns: string | null = NFE_NS): string | undefined => {
  if (!element) return undefined;
  const nodeList = ns ? element.getElementsByTagNameNS(ns, tagName) : element.getElementsByTagName(tagName);
  return nodeList.length > 0 ? nodeList[0]?.textContent?.trim() : undefined;
};

// getTaxDetail and getSpecificTaxData remain unchanged, so they are omitted for brevity in this diff.
// Assume they are present in the actual file.
const getTaxDetail = (parentElement: Element | undefined, taxGroupName: string, taxNamePrefix: string, ns: string | null = NFE_NS): TaxDetail | undefined => {
  if (!parentElement) return undefined;
  const taxGroup = parentElement.getElementsByTagNameNS(ns, taxGroupName)[0] || parentElement.getElementsByTagName(taxGroupName)[0];
  if (!taxGroup) return undefined;
  
  const detail: TaxDetail = {};
  detail.vBC = getText(taxGroup, `vBC${taxNamePrefix}`.replace('vBCISSQN', 'vBC'), ns) || getText(taxGroup, 'vBC', ns) ;
  detail.p = getText(taxGroup, `p${taxNamePrefix}`.replace('pISSQN', 'pAliq'), ns) || getText(taxGroup, `p${taxNamePrefix.substring(0, taxNamePrefix.length-2)}`, ns) || getText(taxGroup, 'pAliq', ns); 
  detail.v = getText(taxGroup, `v${taxNamePrefix}`, ns) || getText(taxGroup, `v${taxNamePrefix.substring(0, taxNamePrefix.length-2)}`, ns);

  if (Object.values(detail).every(val => val === undefined)) {
      const generic_vBC = getText(taxGroup, 'vBC', ns);
      const generic_p = getText(taxGroup, 'pAliq', ns) || getText(taxGroup, 'pRedBC', ns); 
      const generic_v = getText(taxGroup, `v${taxGroupName.replace('ST','')}`,ns) || getText(taxGroup, `v${taxNamePrefix}`, ns) ; 
      if (generic_vBC || generic_p || generic_v) {
          return { vBC: generic_vBC, p: generic_p, v: generic_v };
      }
      return undefined;
  }
  return (detail.vBC || detail.p || detail.v) ? detail : undefined;
};


const getSpecificTaxData = (impostoNode: Element, taxType: 'ICMS' | 'IPI' | 'PIS' | 'COFINS' | 'ISSQN'): Partial<ProdutoNotaFiscal> => {
    const productTax: Partial<ProdutoNotaFiscal> = {};
    const commonNS = NFE_NS;

    if (taxType === 'ICMS') {
        const icmsNode = impostoNode.getElementsByTagNameNS(commonNS, 'ICMS')[0];
        if (icmsNode) {
            const icmsTypes = icmsNode.children; 
            if (icmsTypes.length > 0) {
                const specificIcmsNode = icmsTypes[0]; 
                productTax.ICMS = {
                    orig: getText(specificIcmsNode, 'orig', commonNS),
                    CST: getText(specificIcmsNode, 'CST', commonNS),
                    CSOSN: getText(specificIcmsNode, 'CSOSN', commonNS),
                    modBC: getText(specificIcmsNode, 'modBC', commonNS),
                    vBC: getText(specificIcmsNode, 'vBC', commonNS),
                    p: getText(specificIcmsNode, 'pICMS', commonNS) || getText(specificIcmsNode, 'pRedBC', commonNS),
                    v: getText(specificIcmsNode, 'vICMS', commonNS),
                    vICMSDeson: getText(specificIcmsNode, 'vICMSDeson', commonNS),
                    motDesICMS: getText(specificIcmsNode, 'motDesICMS', commonNS),
                };
            }
        }
         const icmsStNode = impostoNode.getElementsByTagNameNS(commonNS, 'ICMSST')[0] || 
                           (icmsNode?.getElementsByTagNameNS(commonNS, 'ICMSST')[0]) ||
                           (icmsNode?.children.length > 0 && icmsNode.children[0].tagName.includes('ST') ? icmsNode.children[0] : undefined);

        if (icmsStNode) {
             productTax.ICMSST = {
                vBCSTRet: getText(icmsStNode, 'vBCSTRet', commonNS),
                pST: getText(icmsStNode, 'pST', commonNS),
                vICMSSTRet: getText(icmsStNode, 'vICMSSTRet', commonNS),
                vBCSTDest: getText(icmsStNode, 'vBCSTDest', commonNS),
                vICMSSTDest: getText(icmsStNode, 'vICMSSTDest', commonNS),
                vBC: getText(icmsStNode, 'vBCST', commonNS) || getText(icmsStNode, 'vBC', commonNS),
                p: getText(icmsStNode, 'pICMSST', commonNS) || getText(icmsStNode, 'pST', commonNS),
                v: getText(icmsStNode, 'vICMSST', commonNS),
            };
        }
    } else if (taxType === 'IPI') {
        const ipiNode = impostoNode.getElementsByTagNameNS(commonNS, 'IPI')[0];
        if (ipiNode) {
            const ipiTribNode = ipiNode.getElementsByTagNameNS(commonNS, 'IPITrib')[0];
            productTax.IPI = {
                cEnq: getText(ipiNode, 'cEnq', commonNS),
                CST: getText(ipiNode, 'CST', commonNS) || getText(ipiTribNode, 'CST', commonNS),
                vBC: getText(ipiTribNode, 'vBC', commonNS),
                p: getText(ipiTribNode, 'pIPI', commonNS),
                v: getText(ipiTribNode, 'vIPI', commonNS),
            };
        }
    } else if (taxType === 'PIS') {
        const pisNode = impostoNode.getElementsByTagNameNS(commonNS, 'PIS')[0];
        if (pisNode) {
            const pisTypeNode = pisNode.children[0]; 
             if(pisTypeNode){
                productTax.PIS = {
                    CST: getText(pisTypeNode, 'CST', commonNS),
                    vBC: getText(pisTypeNode, 'vBC', commonNS),
                    p: getText(pisTypeNode, 'pPIS', commonNS),
                    v: getText(pisTypeNode, 'vPIS', commonNS),
                };
             }
        }
        const pisStNode = impostoNode.getElementsByTagNameNS(commonNS, 'PISST')[0];
        if(pisStNode){
            productTax.PISST = {
                vBC: getText(pisStNode, 'vBC', commonNS),
                p: getText(pisStNode, 'pPIS', commonNS),
                v: getText(pisStNode, 'vPIS', commonNS),
            };
        }
    } else if (taxType === 'COFINS') {
        const cofinsNode = impostoNode.getElementsByTagNameNS(commonNS, 'COFINS')[0];
        if (cofinsNode) {
             const cofinsTypeNode = cofinsNode.children[0]; 
             if(cofinsTypeNode){
                productTax.COFINS = {
                    CST: getText(cofinsTypeNode, 'CST', commonNS),
                    vBC: getText(cofinsTypeNode, 'vBC', commonNS),
                    p: getText(cofinsTypeNode, 'pCOFINS', commonNS),
                    v: getText(cofinsTypeNode, 'vCOFINS', commonNS),
                };
             }
        }
        const cofinsStNode = impostoNode.getElementsByTagNameNS(commonNS, 'COFINSST')[0];
        if(cofinsStNode){
            productTax.COFINSST = {
                 vBC: getText(cofinsStNode, 'vBC', commonNS),
                 p: getText(cofinsStNode, 'pCOFINS', commonNS),
                 v: getText(cofinsStNode, 'vCOFINS', commonNS),
            };
        }
    } else if (taxType === 'ISSQN') {
        const issqnNode = impostoNode.getElementsByTagNameNS(commonNS, 'ISSQN')[0];
        if (issqnNode) {
            productTax.ISSQN = {
                vBC: getText(issqnNode, 'vBC', commonNS),
                p: getText(issqnNode, 'vAliq', commonNS), 
                v: getText(issqnNode, 'vISSQN', commonNS),
                cListServ: getText(issqnNode, 'cListServ', commonNS),
                cSitTrib: getText(issqnNode, 'cSitTrib', commonNS),
            };
        }
    }
    return productTax;
};


export const parseNFXml = async (xmlString: string, fileName: string): Promise<NotaFiscal> => {
  try {
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlString, "application/xml");

    const errorNode = xmlDoc.getElementsByTagName("parsererror");
    if (errorNode.length > 0) {
      throw new Error(`XML inválido ou malformado: ${errorNode[0].textContent}`);
    }
    
    let rootElement = xmlDoc.documentElement;
    let NFeElement: Element | undefined; // Specifically for the <NFe> tag
    let mainInfoBlock: Element | undefined; // Generic var for <infNFe>, <infNfse>, <infCte>
    let docType: DocumentType = 'UNKNOWN';
    
    // 1. Try to find <NFe> tag, which is common to NFe and NFCe
    if ((rootElement.localName === 'nfeProc' || rootElement.tagName === 'nfeProc') && (rootElement.namespaceURI === NFE_NS || !rootElement.namespaceURI) ) {
        NFeElement = rootElement.getElementsByTagNameNS(NFE_NS, "NFe")[0] || rootElement.getElementsByTagName("NFe")[0];
    } else if ((rootElement.localName === 'NFe' || rootElement.tagName === 'NFe') && (rootElement.namespaceURI === NFE_NS || !rootElement.namespaceURI)) {
        NFeElement = rootElement;
    } else {
        // Fallback: Search for NFe tag anywhere in the document (e.g. if wrapped by another element)
        NFeElement = xmlDoc.getElementsByTagNameNS(NFE_NS, "NFe")[0] || xmlDoc.getElementsByTagName("NFe")[0];
    }

    if (NFeElement) {
        mainInfoBlock = NFeElement.getElementsByTagNameNS(NFE_NS, "infNFe")[0] || NFeElement.getElementsByTagName("infNFe")[0];
        if (mainInfoBlock) {
            const ideNode = mainInfoBlock.getElementsByTagNameNS(NFE_NS, "ide")[0] || mainInfoBlock.getElementsByTagName("ide")[0];
            const mod = getText(ideNode, "mod", NFE_NS);
            if (mod === '65') {
                docType = 'NFCe';
            } else if (mod === '55') {
                docType = 'NFe';
            } else { // If mod is missing, but we have infNFe, make a guess
                docType = fileName.toLowerCase().includes("nfc") ? 'NFCe' : 'NFe';
            }
        }
    }

    // 2. If not NFe/NFCe (mainInfoBlock is still undefined), try NFSe patterns
    if (!mainInfoBlock) {
        const nfsePatterns = ["nfse", "CompNfse", "Comprovante", "InfNfse", "infNfse", "dadosNfse", "InfRps", "GerarNfseEnvio", "ConsultarNfseResposta"];
        const isNFSeLikely = nfsePatterns.some(pattern => rootElement.tagName.toLowerCase().includes(pattern.toLowerCase()) || xmlDoc.getElementsByTagName(pattern).length > 0);

        if (isNFSeLikely) {
            docType = 'NFSe';
            const compNfse = rootElement.getElementsByTagName("CompNfse")[0] || rootElement.getElementsByTagName("Comprovante")[0];
            const nfseNodeInner = compNfse 
                ? (compNfse.getElementsByTagName("Nfse")[0] || compNfse.getElementsByTagName("nfse")[0]) 
                : (rootElement.getElementsByTagName("Nfse")[0] || rootElement.getElementsByTagName("nfse")[0]);
            
            mainInfoBlock = nfseNodeInner 
                ? (nfseNodeInner.getElementsByTagName("InfNfse")[0] || nfseNodeInner.getElementsByTagName("infNfse")[0]) 
                : undefined;

            if (!mainInfoBlock) mainInfoBlock = rootElement.getElementsByTagName("InfRps")[0]; // InfRps is common
            if (!mainInfoBlock) mainInfoBlock = rootElement.getElementsByTagName("infNfse")[0] || rootElement.getElementsByTagName("InfNfse")[0]; // direct search
            if (!mainInfoBlock) mainInfoBlock = rootElement.getElementsByTagName("dadosNfse")[0]; 
            if (!mainInfoBlock) { // If still not found, check if root or a direct child contains key NFSe data points
                 if (getText(rootElement, "Numero", null) && getText(rootElement, "PrestadorServico", null)) {
                    mainInfoBlock = rootElement;
                 } else if (rootElement.children.length > 0 && getText(rootElement.children[0], "Numero", null) && getText(rootElement.children[0], "PrestadorServico", null)) {
                    mainInfoBlock = rootElement.children[0];
                 }
            }
        }
    }

    // 3. If not NFe/NFCe/NFSe, try CTe patterns
    if (!mainInfoBlock) {
        const isCTeLikely = rootElement.tagName.toLowerCase().includes('cte') || xmlDoc.getElementsByTagName("CTe").length > 0;
        if (isCTeLikely) {
            docType = 'CTe';
            const CTeNode = rootElement.getElementsByTagNameNS(NFE_NS, "CTe")[0] || xmlDoc.getElementsByTagName("CTe")[0] || rootElement;
             if (CTeNode) {
                mainInfoBlock = CTeNode.getElementsByTagNameNS(NFE_NS, "infCte")[0] || CTeNode.getElementsByTagName("infCte")[0];
             }
        }
    }

    if (!mainInfoBlock) {
      throw new Error("Estrutura de informações principais (infNFe, infNfse, infCte, etc.) não encontrada.");
    }

    let ide: Element | undefined | null, emit: Element | undefined | null, dest: Element | undefined | null, total: Element | undefined | null, detNodes: HTMLCollectionOf<Element> | Element[] = [];

    // Assign parsing context based on determined docType
    if (docType === 'NFe' || docType === 'NFCe') {
        ide = mainInfoBlock.getElementsByTagNameNS(NFE_NS, "ide")[0] || mainInfoBlock.getElementsByTagName("ide")[0];
        emit = mainInfoBlock.getElementsByTagNameNS(NFE_NS, "emit")[0] || mainInfoBlock.getElementsByTagName("emit")[0];
        dest = mainInfoBlock.getElementsByTagNameNS(NFE_NS, "dest")[0] || mainInfoBlock.getElementsByTagName("dest")[0];
        total = mainInfoBlock.getElementsByTagNameNS(NFE_NS, "total")[0] || mainInfoBlock.getElementsByTagName("total")[0];
        detNodes = mainInfoBlock.getElementsByTagNameNS(NFE_NS, "det") || mainInfoBlock.getElementsByTagName("det");
    } else if (docType === 'NFSe') {
        ide = mainInfoBlock.getElementsByTagName("IdentificacaoNfse")[0] || mainInfoBlock.getElementsByTagName("IdentificacaoRps")[0] || mainInfoBlock;
        emit = mainInfoBlock.getElementsByTagName("PrestadorServico")[0] || mainInfoBlock.getElementsByTagName("Prestador")[0] || mainInfoBlock.getElementsByTagName("emit")[0] || mainInfoBlock.getElementsByTagName("Emitente")[0] || mainInfoBlock;
        dest = mainInfoBlock.getElementsByTagName("TomadorServico")[0] || mainInfoBlock.getElementsByTagName("Tomador")[0] || mainInfoBlock.getElementsByTagName("dest")[0] || mainInfoBlock.getElementsByTagName("Destinatario")[0] || mainInfoBlock;
        total = mainInfoBlock.getElementsByTagName("ValoresNfse")[0] || mainInfoBlock.getElementsByTagName("Valores")[0] || mainInfoBlock.getElementsByTagName("Valor")[0] || mainInfoBlock;
        
        const servicoNode = mainInfoBlock.getElementsByTagName("Servico")[0];
        if (servicoNode) {
            const itensNode = servicoNode.getElementsByTagName("Itens")[0];
            if (itensNode) {
                detNodes = itensNode.getElementsByTagName("ItemListaServico");
            } else {
                detNodes = servicoNode.getElementsByTagName("ItemServico");
            }
             // If Servico node exists but no specific item list, and it has pricing info, treat Servico itself as an item-like node.
            if ((!detNodes || detNodes.length === 0) && (getText(servicoNode, "ValorServicos", null) || getText(servicoNode, "ValorLiquidoNfse", null)) ) {
                detNodes = [servicoNode]; // Treat the single Servico node as the item
            }
        } else {
            detNodes = mainInfoBlock.getElementsByTagName("ItemServico");
        }
        if (!detNodes || detNodes.length === 0) detNodes = mainInfoBlock.getElementsByTagName("item");


    } else if (docType === 'CTe') {
        ide = mainInfoBlock.getElementsByTagNameNS(NFE_NS, "ide")[0] || mainInfoBlock.getElementsByTagName("ide")[0];
        emit = mainInfoBlock.getElementsByTagNameNS(NFE_NS, "emit")[0] || mainInfoBlock.getElementsByTagName("emit")[0];
        dest = mainInfoBlock.getElementsByTagNameNS(NFE_NS, "dest")[0] || mainInfoBlock.getElementsByTagName("dest")[0];
        total = mainInfoBlock.getElementsByTagNameNS(NFE_NS, "vPrest")[0] || mainInfoBlock.getElementsByTagName("vPrest")[0] || mainInfoBlock;
        detNodes = mainInfoBlock.getElementsByTagNameNS(NFE_NS, "infCTeNorm")?.[0]?.getElementsByTagNameNS(NFE_NS, "infDoc") || [];
    }
    
    // Fallbacks if specific structures not found
    ide = ide || mainInfoBlock;
    emit = emit || mainInfoBlock;
    dest = dest || mainInfoBlock;
    total = total || mainInfoBlock;
    detNodes = (detNodes && detNodes.length > 0) ? detNodes : [] as any;


    const notaFiscal: NotaFiscal = {
      id: mainInfoBlock.getAttribute("Id") || fileName + Date.now(),
      fileName: fileName,
      documentType: docType,
      dhEmi: getText(ide, "dhEmi", NFE_NS) || getText(ide, "dEmi", NFE_NS) || getText(ide, "DataEmissao", null) || getText(ide, "dtEmis", null),
      emitCNPJ: getText(emit, "CNPJ", NFE_NS) || getText(emit?.getElementsByTagName("CpfCnpj")[0], "Cnpj", null) || getText(emit, "Cnpj", null),
      emitCPF: getText(emit, "CPF", NFE_NS) || getText(emit?.getElementsByTagName("CpfCnpj")[0], "Cpf", null) || getText(emit, "Cpf", null),
      emitName: getText(emit, "xNome", NFE_NS) || getText(emit, "RazaoSocial", null) || getText(emit, "Nome", null) ,
      destCNPJ: getText(dest, "CNPJ", NFE_NS) || getText(dest?.getElementsByTagName("CpfCnpj")[0], "Cnpj", null) || getText(dest, "Cnpj", null),
      destCPF: getText(dest, "CPF", NFE_NS) || getText(dest?.getElementsByTagName("CpfCnpj")[0], "Cpf", null) || getText(dest, "Cpf", null),
      destName: getText(dest, "xNome", NFE_NS) || getText(dest, "RazaoSocial", null) || getText(dest, "Nome", null),
      vNF: getText(total?.getElementsByTagNameNS(NFE_NS, "ICMSTot")[0], "vNF", NFE_NS) || getText(total, "vServ", null) || getText(total, "ValorServicos", null) || getText(total, "vDoc", null) || getText(total, "vNF", null),
      vTotTrib: getText(total?.getElementsByTagNameNS(NFE_NS, "ICMSTot")[0], "vTotTrib", NFE_NS) || getText(total, "vTotTrib", null) || getText(mainInfoBlock, "valorTributos", null) || getText(total, "ValorTributos", null),
      itens: [],
      xmlContent: xmlString,
      tipo: 'DESCONHECIDO',
      naturezaOperacao: getText(ide, "natOp", NFE_NS) || getText(mainInfoBlock, "NaturezaOperacao", null) || getText(servicoNodeIfNFSe(mainInfoBlock, docType), "NaturezaOperacao", null)
    };
    
    function servicoNodeIfNFSe(mainBlock: Element, type: DocumentType): Element | null {
        return type === 'NFSe' ? (mainBlock.getElementsByTagName("Servico")[0] || mainBlock) : null;
    }

    for (let i = 0; i < detNodes.length; i++) {
      const det = detNodes[i];
      // For NFe/NFCe, prodNode is under 'det'. For some NFSe, 'det' itself is the item (e.g. if detNodes is [servicoNode]).
      const prodNode = (docType === 'NFe' || docType === 'NFCe') ? (det.getElementsByTagNameNS(NFE_NS, "prod")[0] || det.getElementsByTagName("prod")[0]) : null;
      const currentItemNode = prodNode || det; // If prodNode not found (e.g. NFSe item), currentItemNode is det itself.


      if (currentItemNode) {
        const item: ProdutoNotaFiscal = {
          cProd: getText(currentItemNode, "cProd", NFE_NS) || getText(currentItemNode, "CodigoProduto", null) || getText(currentItemNode, "CodigoItem", null) || getText(currentItemNode.getElementsByTagName("IdentificacaoItem")[0], "Codigo", null) || `ITEM_${i+1}`,
          xProd: getText(currentItemNode, "xProd", NFE_NS) || getText(currentItemNode, "DescricaoProduto", null) || getText(currentItemNode, "Descricao", null) || getText(currentItemNode, "Discriminacao", null) || getText(currentItemNode, "DiscriminacaoServico", null) || 'Item sem descrição',
          NCM: getText(currentItemNode, "NCM", NFE_NS) || getText(currentItemNode, "CodigoNCM", null) || '',
          qCom: getText(currentItemNode, "qCom", NFE_NS) || getText(currentItemNode, "Quantidade", null) || '1',
          uCom: getText(currentItemNode, "uCom", NFE_NS) || getText(currentItemNode, "Unidade", null) || 'UN',
          vUnCom: getText(currentItemNode, "vUnCom", NFE_NS) || getText(currentItemNode, "ValorUnitario", null) || getText(currentItemNode, "PrecoUnitario", null) || '0',
          vProd: getText(currentItemNode, "vProd", NFE_NS) || getText(currentItemNode, "ValorTotal", null) || getText(currentItemNode, "PrecoTotal", null) || getText(currentItemNode, "ValorServicos", null) || '0',
        };

        const impostoNode = det.getElementsByTagNameNS(NFE_NS, "imposto")[0] || det.getElementsByTagName("imposto")[0] || det.getElementsByTagName("Impostos")[0] || det.getElementsByTagName("tributos")[0] || currentItemNode.getElementsByTagName("Valores")[0]; // For NFSe, taxes might be in <Valores> of item
        if (impostoNode) {
          Object.assign(item, getSpecificTaxData(impostoNode, 'ICMS'));
          Object.assign(item, getSpecificTaxData(impostoNode, 'IPI'));
          Object.assign(item, getSpecificTaxData(impostoNode, 'PIS'));
          Object.assign(item, getSpecificTaxData(impostoNode, 'COFINS'));
          Object.assign(item, getSpecificTaxData(impostoNode, 'ISSQN'));

           if (!notaFiscal.vTotTrib && getText(impostoNode, "vTotTrib", NFE_NS)) {
             notaFiscal.vTotTrib = (parseFloat(notaFiscal.vTotTrib || "0") + parseFloat(getText(impostoNode, "vTotTrib", NFE_NS) || "0")).toString();
           }
        }
        notaFiscal.itens.push(item);
      }
    }
    
    if (!notaFiscal.vNF || parseFloat(notaFiscal.vNF) === 0) { // Check if vNF is missing or zero
        if (docType === 'NFSe') {
            const vServicos = getText(total, "ValorServicos", null) || getText(mainInfoBlock, "ValorServicos", null);
            const vLiquidoNfse = getText(total, "ValorLiquidoNfse", null) || getText(mainInfoBlock, "ValorLiquidoNfse", null);
            if (vLiquidoNfse) {
                notaFiscal.vNF = vLiquidoNfse;
            } else if (vServicos) {
                notaFiscal.vNF = vServicos;
            } else if (notaFiscal.itens.length > 0) {
                notaFiscal.vNF = notaFiscal.itens.reduce((sum, item) => sum + parseFloat(item.vProd || '0'), 0).toFixed(2);
            }
        } else if (docType === 'CTe') { 
            const vPrest = getText(total, "vTPrest", NFE_NS) || getText(total, "vRec", NFE_NS);
            if (vPrest) notaFiscal.vNF = vPrest;
        } else if (docType === 'NFe' || docType === 'NFCe') { // For NFe/NFCe, if vNF in ICMSTot is zero/missing, sum items
             if (notaFiscal.itens.length > 0) {
                notaFiscal.vNF = notaFiscal.itens.reduce((sum, item) => sum + parseFloat(item.vProd || '0'), 0).toFixed(2);
            }
        }
    }


    return notaFiscal;

  } catch (error) {
    console.error(`Erro ao parsear XML ${fileName}:`, error);
    return {
      id: fileName + Date.now(),
      fileName: fileName,
      documentType: 'UNKNOWN',
      itens: [],
      xmlContent: xmlString,
      error: `Falha ao processar XML: ${(error as Error).message}`,
      tipo: 'DESCONHECIDO',
    };
  }
};
