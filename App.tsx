
import React, { useState, useEffect, useCallback } from 'react';
import { ProdutoMaster, NotaFiscal, ChatMessage, AppSection, Discrepancy, DocumentType, ReportData, SalesEvolutionDataPoint } from './types';
import { PRODUCT_MASTER_COLUMNS, PRODUCT_MASTER_COLUMN_DESCRIPTIONS, INITIAL_CHAT_MESSAGE } from './constants';
import { parseProductCSV } from './services/fileParserService';
import { parseNFXml } from './services/xmlParserService';
import {
    classifyNoteTypeWithAI, getAIChatResponse, getFiscalMonitoringSummaryAI,
    analyzePotentialCreditsAI, getReportSummaryAI, getProactiveInsightsAI
} from './services/geminiService';
import * as analyticsService from './services/analyticsService';
import FileUpload from './components/FileUpload';
import ProductList from './components/ProductList';
import FiscalNoteList from './components/FiscalNoteList';
import Ai<PERSON>hat from './components/AiChat';
import SettingsModal from './components/SettingsModal';
import {
  LoadingSpinner, AlertMessage, InfoIcon, UploadIcon, ChatBubbleLeftEllipsisIcon, TableCellsIcon,
  DocumentTextIcon, CubeIcon, Bars3Icon, XMarkIcon, BuildingStorefrontIcon, ChartBarIcon, CurrencyDollarIcon, TagIcon,
  ArrowTrendingUpIcon, ArrowSmallLeftIcon,
  ExclamationTriangleIcon, LightBulbIcon, DocumentMagnifyingGlassIcon
} from './components/Icons';
import DashboardCard from './components/DashboardCard';
import { supabase } from './src/services/supabaseClient';
import supabaseService from './src/services/supabaseService';
import Auth from './src/components/Auth';
import type { User } from '@supabase/supabase-js';

const App: React.FC = () => {
  const [userCompanyCNPJ, setUserCompanyCNPJ] = useState<string>('');
  const [products, setProducts] = useState<ProdutoMaster[]>([]);
  const [fiscalNotes, setFiscalNotes] = useState<NotaFiscal[]>([]);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([INITIAL_CHAT_MESSAGE]);

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isAISuggesting, setIsAISuggesting] = useState<boolean>(false); // For background AI tasks like classification
  const [isAIResponding, setIsAIResponding] = useState<boolean>(false); // For active AI interactions (chat, summaries)
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const [activeSection, setActiveSection] = useState<AppSection>(AppSection.Dashboard);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState<boolean>(false);
  const [isProductTemplateModalOpen, setIsProductTemplateModalOpen] = useState<boolean>(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(false);

  // States for analytics data
  const [dashboardSummary, setDashboardSummary] = useState<any>({});
  const [taxDistribution, setTaxDistribution] = useState<Record<string, number>>({});
  const [fiscalDiscrepancies, setFiscalDiscrepancies] = useState<Discrepancy[]>([]);
  const [currentReport, setCurrentReport] = useState<ReportData | null>(null);
  const [aiSummaryForReport, setAiSummaryForReport] = useState<string>('');

  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);

  const handleLogout = async () => {
    await supabase.auth.signOut();
  };

  useEffect(() => {
    const checkUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setCurrentUser(user);
      setIsAuthenticated(!!user);

      if (user) {
        // Carrega as configurações do usuário
        try {
          const settings = await supabaseService.getUserSettings(user.id);
          if (settings?.userCompanyCNPJ) {
            setUserCompanyCNPJ(settings.userCompanyCNPJ);
          }
        } catch (error) {
          console.error("Erro ao carregar configurações:", error);
        }

        // Carrega produtos e notas fiscais
        loadProductsAndNotes();
      }
    };

    checkUser();

    // Configura listener para mudanças de autenticação
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        const user = session?.user;
        setCurrentUser(user || null);
        setIsAuthenticated(!!user);

        if (event === 'SIGNED_IN' && user) {
          // Carrega dados quando o usuário faz login
          loadProductsAndNotes();
        } else if (event === 'SIGNED_OUT') {
          // Limpa dados quando o usuário faz logout
          setProducts([]);
          setFiscalNotes([]);
        }
      }
    );

    return () => {
      authListener.subscription.unsubscribe();
    };
  }, []);

  if (!isAuthenticated) {
    return <Auth onLogin={() => setIsAuthenticated(true)} />;
  }

  const loadProductsAndNotes = async () => {
    try {
      const { data: productsData } = await supabaseService.getProducts();
      setProducts(productsData || []);

      const { data: notesData } = await supabaseService.getFiscalNotes();
      setFiscalNotes(notesData || []);
    } catch (error) {
      console.error("Erro ao carregar produtos e notas fiscais:", error);
    }
  };

  // Recalculate analytics data when notes or products change
  useEffect(() => {
    if (fiscalNotes.length > 0 || products.length > 0) {
      const summary = analyticsService.getGeneralSummary(fiscalNotes, products);
      setDashboardSummary(summary);
      const taxes = analyticsService.calculateTotalTaxesByType(fiscalNotes);
      setTaxDistribution(taxes);
      const discrepancies = analyticsService.performFiscalMonitoring(fiscalNotes, products);
      setFiscalDiscrepancies(discrepancies);
    } else { // If no notes or products, reset summary
      setDashboardSummary({ salesEvolutionData: [] }); // Ensure salesEvolutionData is initialized
      setTaxDistribution({});
      setFiscalDiscrepancies([]);
    }
  }, [fiscalNotes, products]);


  const handleSetUserCompanyCNPJ = (cnpj: string) => {
    setUserCompanyCNPJ(cnpj);
    localStorage.setItem('userCompanyCNPJ', cnpj);
    setSuccessMessage("CNPJ da empresa salvo com sucesso!");
    setTimeout(() => setSuccessMessage(null), 3000);
    // Re-classify notes if CNPJ changes and notes exist
    if(fiscalNotes.length > 0) {
        // Mark existing notes for re-classification
        setFiscalNotes(prevNotes => prevNotes.map(n => ({...n, tipo: 'DESCONHECIDO'})));
    }
  };

  const handleProductFileUpload = async (file: File) => {
    setIsLoading(true); setError(null); setSuccessMessage(null);
    try {
      if (file.type !== 'text/csv') throw new Error('Formato de arquivo inválido. Por favor, envie um arquivo CSV.');
      const parsedProducts = await parseProductCSV(file);
      setProducts(prevProducts => {
        const existingCodes = new Set(prevProducts.map(p => p.CODIGO_INTERNO_PRODUTO));
        const newUniqueProducts = parsedProducts.filter(p => !existingCodes.has(p.CODIGO_INTERNO_PRODUTO));
        return [...prevProducts, ...newUniqueProducts];
      });
      setSuccessMessage(`${parsedProducts.length} produtos processados. ${parsedProducts.length - products.filter(p => parsedProducts.find(np => np.CODIGO_INTERNO_PRODUTO === p.CODIGO_INTERNO_PRODUTO)).length} novos adicionados.`);
    } catch (e) { setError((e as Error).message); }
    finally { setIsLoading(false); setTimeout(() => {setSuccessMessage(null); setError(null)}, 3000); }
  };

  const handleXmlFilesUpload = async (files: FileList) => {
    setIsLoading(true); setError(null); setSuccessMessage(null);
    let countSuccess = 0; let countError = 0;
    const newNotesPromises = Array.from(files).map(async file => {
      try {
        if (file.type !== 'text/xml' && file.type !== 'application/xml') {
          countError++;
          return { id: file.name + Date.now(), fileName: file.name, documentType: 'UNKNOWN' as DocumentType, itens: [], xmlContent: '', error: 'Formato inválido (não XML).', tipo: 'DESCONHECIDO' as const };
        }
        const xmlText = await file.text();
        const parsedNote = await parseNFXml(xmlText, file.name);
        if (parsedNote.error) countError++; else countSuccess++;
        return parsedNote;
      } catch (e) {
        countError++;
        return { id: file.name + Date.now(), fileName: file.name, documentType: 'UNKNOWN' as DocumentType, itens: [], xmlContent: '', error: `Erro ao processar ${file.name}: ${(e as Error).message}`, tipo: 'DESCONHECIDO' as const };
      }
    });
    const resolvedNewNotes = await Promise.all(newNotesPromises);
    setFiscalNotes(prevNotes => {
        const existingIds = new Set(prevNotes.map(n => n.id));
        const uniqueNewNotes = resolvedNewNotes.filter(n => !existingIds.has(n.id));
        return [...prevNotes, ...uniqueNewNotes];
    });
    setSuccessMessage(`${countSuccess} notas fiscais processadas. ${countError > 0 ? `${countError} com erro.` : ''}`);
    setIsLoading(false);
    setTimeout(() => {setSuccessMessage(null); setError(null)}, 5000);
  };

  const classifyAllNotes = useCallback(async () => {
    if (!userCompanyCNPJ || fiscalNotes.length === 0) return;
    const notesToClassify = fiscalNotes.filter(note => (!note.tipo || note.tipo === 'DESCONHECIDO') && !note.error);
    if (notesToClassify.length === 0) return;

    setIsAISuggesting(true);
    const classificationPromises = notesToClassify.map(note =>
        classifyNoteTypeWithAI(note, userCompanyCNPJ).then(tipo => ({ id: note.id, tipo }))
    );
    const classifications = await Promise.all(classificationPromises);

    setFiscalNotes(prevNotes => {
      const notesMap = new Map(prevNotes.map(n => [n.id, n]));
      classifications.forEach(c => {
        const note = notesMap.get(c.id);
        if (note) notesMap.set(c.id, { ...note, tipo: c.tipo });
      });
      return Array.from(notesMap.values());
    });
    setIsAISuggesting(false);
  }, [userCompanyCNPJ, fiscalNotes]); // fiscalNotes is a dependency now

  useEffect(() => {
    if (userCompanyCNPJ && fiscalNotes.some(note => (!note.tipo || note.tipo === 'DESCONHECIDO') && !note.error)) {
      classifyAllNotes();
    }
  }, [userCompanyCNPJ, fiscalNotes, classifyAllNotes]);


  const handleSendMessage = async (messageText: string) => {
    const userMessage: ChatMessage = { id: Date.now().toString(), sender: 'user', text: messageText, timestamp: new Date() };
    setChatMessages(prev => [...prev, userMessage]);
    setIsAIResponding(true); setError(null);
    try {
      // Basic context preparation (can be significantly improved with query analysis)
      let context = "";
      if (messageText.toLowerCase().includes("produto") && products.length > 0) {
        context = `Contexto de Produtos (amostra): ${JSON.stringify(products.slice(0,2).map(p => ({codigo: p.CODIGO_INTERNO_PRODUTO, desc: p.DESCRICAO_PRODUTO, ncm: p.NCM})))}`;
      } else if (messageText.toLowerCase().includes("nota") && fiscalNotes.length > 0) {
        context = `Contexto de Notas Fiscais (amostra): ${JSON.stringify(fiscalNotes.slice(0,2).map(n => ({id: n.id, tipoDoc: n.documentType, data: n.dhEmi, valor: n.vNF, tipoAI: n.tipo})))}`;
      } else {
        context = `Resumo Geral (se relevante): ${JSON.stringify(dashboardSummary)}`;
      }

      const aiResponseText = await getAIChatResponse(messageText, context);
      const aiMessage: ChatMessage = { id: (Date.now() + 1).toString(), sender: 'ai', text: aiResponseText, timestamp: new Date() };
      setChatMessages(prev => [...prev, aiMessage]);
    } catch (e) {
      setError((e as Error).message);
      const aiErrorMessage: ChatMessage = { id: (Date.now() + 1).toString(), sender: 'ai', text: "Desculpe, não consegui processar sua solicitação no momento.", timestamp: new Date() };
      setChatMessages(prev => [...prev, aiErrorMessage]);
    } finally {
      setIsAIResponding(false);
    }
  };

  const handleAiReportAction = async (actionType: 'summarize_discrepancies' | 'analyze_credits' | 'summarize_current_report' | 'proactive_insights') => {
    setIsAIResponding(true); setAiSummaryForReport(''); setError(null);
    try {
        let summary = "Aguarde, processando com IA...";
        if (actionType === 'summarize_discrepancies') {
            if (fiscalDiscrepancies.length === 0) throw new Error("Nenhuma discrepância para resumir.");
            summary = await getFiscalMonitoringSummaryAI(fiscalDiscrepancies);
        } else if (actionType === 'analyze_credits') {
            // Prepare very compact data for credit analysis
            const creditContext = {
                totalPISBaseEntrada: fiscalNotes.filter(n=>n.tipo==='ENTRADA').flatMap(n=>n.itens).reduce((sum,i)=>sum+parseFloat(i.PIS?.vBC||'0'),0),
                totalCOFINSBaseEntrada: fiscalNotes.filter(n=>n.tipo==='ENTRADA').flatMap(n=>n.itens).reduce((sum,i)=>sum+parseFloat(i.COFINS?.vBC||'0'),0),
                countNotasEntrada: fiscalNotes.filter(n=>n.tipo==='ENTRADA').length
            };
            summary = await analyzePotentialCreditsAI(creditContext);
        } else if (actionType === 'summarize_current_report' && currentReport) {
            summary = await getReportSummaryAI(currentReport, currentReport.title);
        } else if (actionType === 'proactive_insights') {
            summary = await getProactiveInsightsAI(dashboardSummary);
        } else {
            summary = "Ação de IA não reconhecida ou dados insuficientes.";
        }
        setAiSummaryForReport(summary);
    } catch (e) {
        setError((e as Error).message);
        setAiSummaryForReport(`Erro ao obter resposta da IA: ${(e as Error).message}`);
    } finally {
        setIsAIResponding(false);
    }
  };

  const renderSectionHeader = () => {
    if (activeSection === AppSection.Dashboard) {
      return (
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-800">Dashboard Fiscal</h1>
              <p className="text-sm text-gray-500 mt-1">Análise inteligente das suas notas fiscais</p>
            </div>
            <div className="flex space-x-3 mt-4 sm:mt-0">
              <button onClick={() => setActiveSection(AppSection.Uploads)} className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg flex items-center text-sm shadow hover:shadow-md transition-all">
                <UploadIcon className="w-5 h-5 mr-2" /> Importar Dados
              </button>
              <button onClick={() => setActiveSection(AppSection.Relatorios)} className="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg flex items-center text-sm border border-gray-300 shadow hover:shadow-md transition-all">
                <ChartBarIcon className="w-5 h-5 mr-2" /> Ver Relatórios
              </button>
            </div>
          </div>
        </div>
      );
    }
    return <h1 className="text-3xl font-semibold text-gray-800 mb-6">{activeSection}</h1>;
  };

  const renderReportData = (report: ReportData | null) => {
    if (!report) return <p className="text-gray-500">Selecione um relatório para visualizar.</p>;
    return (
        <div className="mt-4">
            <h3 className="text-lg font-semibold text-gray-700 mb-2">{report.title}</h3>
            {report.summary && Object.entries(report.summary).map(([key, value]) => (
                <p key={key} className="text-sm text-gray-600"><strong>{key}:</strong> {typeof value === 'number' ? value.toLocaleString('pt-BR', {minimumFractionDigits:2, maximumFractionDigits:2}) : value}</p>
            ))}
            {report.columns && report.data.length > 0 && (
                <div className="overflow-x-auto mt-3">
                    <table className="min-w-full text-sm divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>{report.columns.map(col => <th key={col.key} className="px-4 py-2 text-left font-medium text-gray-600">{col.label}</th>)}</tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {report.data.slice(0,10).map((row, rowIndex) => ( // Display first 10 rows for brevity
                                <tr key={rowIndex}>{report.columns!.map(col => <td key={col.key} className="px-4 py-2 text-gray-700 whitespace-nowrap">{typeof row[col.key] === 'number' ? row[col.key].toLocaleString('pt-BR', {minimumFractionDigits:2, maximumFractionDigits:2}) : String(row[col.key] ?? '-')}</td>)}</tr>
                            ))}
                        </tbody>
                    </table>
                    {report.data.length > 10 && <p className="text-xs text-gray-500 mt-1">Mostrando 10 de {report.data.length} registros.</p>}
                </div>
            )}
             <button
                onClick={() => handleAiReportAction('summarize_current_report')}
                disabled={isAIResponding || !currentReport}
                className="mt-4 bg-blue-500 hover:bg-blue-600 text-white font-medium py-1.5 px-3 rounded-md text-xs flex items-center disabled:bg-gray-300"
            >
                {isAIResponding && aiSummaryForReport.includes("Aguarde") ? <LoadingSpinner className="w-4 h-4 mr-1.5"/> : <LightBulbIcon className="w-4 h-4 mr-1.5"/>}
                Obter Resumo com IA
            </button>
        </div>
    );
  };


  const renderSection = () => {
    switch (activeSection) {
      case AppSection.Dashboard:
        const activeProductsCount = products.filter(p => p.STATUS_PRODUTO?.toLowerCase() === 'ativo').length;

        let salesTrend;
        if (!userCompanyCNPJ && fiscalNotes.length > 0 && parseFloat(dashboardSummary.totalSalesValue || '0') === 0) {
            salesTrend = { direction: 'neutral' as const, text: "Configure CNPJ para classificar" };
        } else if (dashboardSummary.classifiedSalesCount !== undefined) {
            salesTrend = { direction: parseFloat(dashboardSummary.totalSalesValue || '0') > 0 ? 'up' as const : 'neutral' as const, text: `${dashboardSummary.classifiedSalesCount} notas de saída` };
        }

        let purchaseTrend;
        if (!userCompanyCNPJ && fiscalNotes.length > 0 && parseFloat(dashboardSummary.totalPurchaseValue || '0') === 0) {
            purchaseTrend = { direction: 'neutral' as const, text: "Configure CNPJ para classificar" };
        } else if (dashboardSummary.classifiedPurchasesCount !== undefined) {
            purchaseTrend = { direction: parseFloat(dashboardSummary.totalPurchaseValue || '0') > 0 ? 'down' as const : 'neutral' as const, text: `${dashboardSummary.classifiedPurchasesCount} notas de entrada` };
        }

        const salesEvolutionData: SalesEvolutionDataPoint[] = dashboardSummary.salesEvolutionData || [];
        const maxSalesForBarHeight = salesEvolutionData.length > 0 ? Math.max(...salesEvolutionData.map(d => d.totalSales), 0) : 1;


        return (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 mb-6">
              <DashboardCard title="Total de Notas" value={fiscalNotes.length} icon={<DocumentTextIcon className="w-7 h-7 text-blue-500" />} trend={{ direction: 'neutral', text: `${fiscalNotes.filter(n=>!n.error).length} válidas` }} />
              <DashboardCard
                title="Vendas Totais"
                value={`R$ ${parseFloat(dashboardSummary.totalSalesValue || '0').toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                icon={<ArrowTrendingUpIcon className="w-7 h-7 text-green-500" />}
                trend={salesTrend}
              />
              <DashboardCard
                title="Compras Totais"
                value={`R$ ${parseFloat(dashboardSummary.totalPurchaseValue || '0').toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                icon={<CurrencyDollarIcon className="w-7 h-7 text-orange-500" />}
                trend={purchaseTrend}
              />
              <DashboardCard title="Impostos Totais" value={`R$ ${parseFloat(dashboardSummary.totalTaxSum || '0').toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`} icon={<TagIcon className="w-7 h-7 text-purple-500" />} />
            </div>
             <div className="mb-6">
                 <button
                    onClick={() => handleAiReportAction('proactive_insights')}
                    disabled={isAIResponding}
                    className="bg-purple-500 hover:bg-purple-600 text-white font-medium py-2 px-4 rounded-lg text-sm flex items-center disabled:bg-gray-300 shadow"
                >
                    {isAIResponding && aiSummaryForReport.includes("Aguarde") ? <LoadingSpinner className="w-5 h-5 mr-2"/> : <LightBulbIcon className="w-5 h-5 mr-2"/>}
                    Buscar Insights Gerais com IA
                </button>
             </div>

            {isAISuggesting && <div className="p-3 mb-4 text-sm text-blue-700 bg-blue-100 rounded-md flex items-center"><LoadingSpinner className="w-5 h-5 mr-2"/> IA processando classificações em segundo plano...</div>}
            {isAIResponding && aiSummaryForReport && <div className="p-3 mb-4 text-sm text-indigo-700 bg-indigo-100 rounded-md"><pre className="whitespace-pre-wrap font-sans">{aiSummaryForReport}</pre></div>}

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
              <div className="lg:col-span-2 bg-white p-6 rounded-xl shadow-lg">
                <h3 className="text-lg font-semibold text-gray-700 mb-1">Evolução das Vendas</h3>
                <p className="text-xs text-gray-500 mb-4">Total de vendas por período (mês/ano).</p>
                {salesEvolutionData.length > 0 ? (
                    <div className="h-64 bg-gray-50 p-4 rounded-md flex items-end space-x-2 overflow-x-auto">
                        {salesEvolutionData.map(dataPoint => {
                            const barHeight = Math.max(5, (dataPoint.totalSales / maxSalesForBarHeight) * 100); // Min height 5%
                            return (
                                <div key={dataPoint.period} className="flex flex-col items-center flex-shrink-0 w-16 group" title={`Período: ${dataPoint.period}\nVendas: R$ ${dataPoint.totalSales.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`}>
                                    <div
                                        className="w-8 bg-blue-400 hover:bg-blue-500 transition-all rounded-t-md"
                                        style={{ height: `${barHeight}%` }}
                                        aria-label={`Vendas de R$ ${dataPoint.totalSales.toLocaleString('pt-BR')} no período ${dataPoint.period}`}
                                    >
                                    </div>
                                    <p className="text-xs text-gray-600 mt-1 truncate group-hover:font-semibold">{dataPoint.period}</p>
                                </div>
                            );
                        })}
                    </div>
                ) : (
                    <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center"><p className="text-gray-400 text-sm">Sem dados de vendas para exibir evolução.</p></div>
                )}
              </div>
              <div className="bg-white p-6 rounded-xl shadow-lg">
                <h3 className="text-lg font-semibold text-gray-700 mb-1">Distribuição de Tributos</h3>
                 <p className="text-xs text-gray-500 mb-4">Total de impostos por tipo.</p>
                {Object.keys(taxDistribution).length > 0 ? (
                    <div className="space-y-1 max-h-64 overflow-y-auto">
                    {Object.entries(taxDistribution).filter(([,value]) => Number(value) > 0).map(([key, value]) => (
                        <div key={key} className="flex justify-between text-sm text-gray-600 hover:bg-gray-50 p-1 rounded">
                            <span className="font-medium">{key}:</span>
                            <span>R$ {Number(value).toLocaleString('pt-BR', {minimumFractionDigits:2, maximumFractionDigits:2})}</span>
                        </div>
                    ))}
                    </div>
                ) : <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center"><p className="text-gray-400 text-sm">Sem dados de impostos para exibir distribuição.</p></div>}
              </div>
            </div>
            {/* Simplified Recent Notes & Products for brevity */}
          </>
        );
      case AppSection.Uploads:
        return (
          <div className="space-y-8">
            <div>
              <h2 className="text-2xl font-semibold text-gray-700 mb-4">Upload de Cadastro de Produtos (CSV)</h2>
              <FileUpload onFileSelect={handleProductFileUpload} acceptedFileTypes=".csv" buttonText="Selecionar CSV de Produtos" label="Arraste e solte o arquivo CSV aqui ou clique para selecionar."/>
              <button onClick={() => setIsProductTemplateModalOpen(true)} className="mt-2 text-sm text-blue-600 hover:text-blue-800 flex items-center" aria-label="Ver instruções do modelo CSV">
                <InfoIcon className="w-4 h-4 mr-1" /> Ver Instruções do Modelo CSV
              </button>
            </div>
            <div>
              <h2 className="text-2xl font-semibold text-gray-700 mb-4">Upload de Documentos Fiscais (XML)</h2>
              <FileUpload onFilesSelect={handleXmlFilesUpload} acceptedFileTypes=".xml" buttonText="Selecionar XMLs (NFe, NFCe, NFSe, CTe)" label="Arraste e solte os arquivos XML aqui ou clique para selecionar." multiple={true}/>
              {!userCompanyCNPJ && <p className="text-sm text-yellow-600 mt-2 flex items-center"><InfoIcon className="w-4 h-4 mr-1"/>Configure o CNPJ da sua empresa nas Configurações para classificação automática das notas (Entrada/Saída).</p>}
            </div>
          </div>
        );
      case AppSection.Products:
        return <ProductList products={products} />;
      case AppSection.FiscalNotes:
         return <FiscalNoteList notes={fiscalNotes} isLoadingAI={isAISuggesting} />;
      case AppSection.Relatorios:
        return (
          <div className="bg-white p-6 rounded-xl shadow-lg">
            <h2 className="text-xl font-semibold text-gray-700 mb-4">Central de Relatórios e Análises</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <button onClick={() => setCurrentReport(analyticsService.getSalesByProductReport(fiscalNotes))} className="p-3 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-md text-sm font-medium text-left">Vendas por Produto</button>
                <button onClick={() => setCurrentReport(analyticsService.getPurchasesByProductReport(fiscalNotes))} className="p-3 bg-green-100 hover:bg-green-200 text-green-700 rounded-md text-sm font-medium text-left">Compras por Produto</button>
                <button onClick={() => {
                    setCurrentReport({ title: "Monitoramento Fiscal", data: fiscalDiscrepancies, columns: [ {key: 'noteFileName', label:'Nota'}, {key:'itemDescription', label:'Item'}, {key: 'field', label:'Campo Divergente'}, {key:'valueInNote', label:'Valor Nota'}, {key:'valueInMaster', label:'Valor Cadastro'}, {key:'message', label:'Detalhe'} ] });
                    setAiSummaryForReport(''); // Clear previous summary
                }} className="p-3 bg-yellow-100 hover:bg-yellow-200 text-yellow-800 rounded-md text-sm font-medium text-left flex items-center">
                    <ExclamationTriangleIcon className="w-5 h-5 mr-2"/> Monitoramento Fiscal ({fiscalDiscrepancies.length})
                </button>
                 <button onClick={() => {
                    setCurrentReport({ title: "Análise de Créditos (IA)", data:[], summary: {info: "Esta é uma análise qualitativa pela IA."} });
                    handleAiReportAction('analyze_credits');
                 }} className="p-3 bg-indigo-100 hover:bg-indigo-200 text-indigo-700 rounded-md text-sm font-medium text-left flex items-center">
                    <DocumentMagnifyingGlassIcon className="w-5 h-5 mr-2"/> Analisar Créditos Potenciais (IA)
                </button>
                 <button onClick={() => {
                     setActiveSection(AppSection.FiscalNotes);
                     setCurrentReport(null);
                 }} className="p-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md text-sm font-medium text-left">
                    Ver Todas as Notas Fiscais
                </button>
            </div>

            {isAIResponding && aiSummaryForReport && <div className="p-3 my-4 text-sm text-indigo-700 bg-indigo-100 rounded-md"><pre className="whitespace-pre-wrap font-sans">{aiSummaryForReport}</pre></div>}

            {currentReport ? renderReportData(currentReport) : <p className="text-gray-500">Selecione um tipo de relatório acima.</p>}

          </div>
        );
      case AppSection.AIChat:
        return <AiChat messages={chatMessages} onSendMessage={handleSendMessage} isLoading={isAIResponding} />;
      default:
        return <p>Seção não encontrada.</p>;
    }
  };

  interface NavLinkProps { section: AppSection; label: string; icon: React.ReactElement<{ className?: string }>;}
  const NavLink: React.FC<NavLinkProps> = ({ section, label, icon }) => (
    <button onClick={() => { setActiveSection(section); setIsSidebarOpen(false); setCurrentReport(null); setAiSummaryForReport('');}}
      className={`flex items-center space-x-3 px-3 py-2.5 rounded-lg transition-all duration-200 w-full text-left group ${activeSection === section ? 'bg-blue-600 text-white shadow-md hover:bg-blue-700' : 'text-gray-600 hover:bg-blue-50 hover:text-blue-700'}`}
      aria-current={activeSection === section ? "page" : undefined}>
      {React.cloneElement(icon, { className: `w-5 h-5 ${activeSection === section ? 'text-white' : 'text-gray-400 group-hover:text-blue-700'}` })}
      <span className="font-medium text-sm">{label}</span>
    </button>
  );

  return (
    <div className="flex h-screen bg-slate-100">
      <aside className={`fixed inset-y-0 left-0 z-30 w-60 bg-white shadow-lg transform ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'} transition-transform duration-300 ease-in-out md:relative md:translate-x-0 md:shadow-none border-r border-gray-200`}>
        <div className="flex items-center space-x-2 p-4 border-b border-gray-200">
          <BuildingStorefrontIcon className="w-8 h-8 text-blue-600" />
          <div><h1 className="text-lg font-bold text-blue-700">FiscalIA</h1><p className="text-xs text-gray-500">Gestão Inteligente</p></div>
        </div>
        <nav className="mt-4 px-3 space-y-1.5">
          <NavLink section={AppSection.Dashboard} label="Dashboard" icon={<TableCellsIcon />} />
          <NavLink section={AppSection.Uploads} label="Importar Dados" icon={<UploadIcon />} />
          <NavLink section={AppSection.Products} label="Produtos" icon={<CubeIcon />} />
          {/* <NavLink section={AppSection.FiscalNotes} label="Notas Fiscais" icon={<DocumentTextIcon />} /> */}
          <NavLink section={AppSection.Relatorios} label="Relatórios" icon={<ChartBarIcon />} />
          <NavLink section={AppSection.AIChat} label="Chat IA" icon={<ChatBubbleLeftEllipsisIcon />} />
        </nav>
        <div className="absolute bottom-0 left-0 w-full p-3 border-t border-gray-200">
          <button
            onClick={handleLogout}
            className="w-full flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-100 hover:text-gray-700"
          >
            <ArrowSmallLeftIcon className="w-5 h-5 mr-2" />
            Sair
          </button>
        </div>
      </aside>
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="bg-white shadow-sm p-3.5 md:hidden border-b border-gray-200">
          <button onClick={() => setIsSidebarOpen(!isSidebarOpen)} className="text-gray-600 focus:outline-none" aria-label={isSidebarOpen ? "Fechar menu lateral" : "Abrir menu lateral"}>
            {isSidebarOpen ? <XMarkIcon className="w-6 h-6" /> : <Bars3Icon className="w-6 h-6" />}
          </button>
        </header>
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-slate-100 p-6" id="main-content" role="main">
          <div className="container mx-auto max-w-7xl">
            {renderSectionHeader()}
            {(isLoading || (isAISuggesting && activeSection !== AppSection.Dashboard) ) &&
              <div className="fixed inset-0 bg-gray-500 bg-opacity-50 flex items-center justify-center z-50" role="status" aria-live="polite">
                <LoadingSpinner className="w-12 h-12 text-white"/><p className="ml-3 text-white text-lg">Processando...</p>
              </div>}
            {error && <AlertMessage type="error" message={error} onClose={() => setError(null)} />}
            {successMessage && <AlertMessage type="success" message={successMessage} onClose={() => setSuccessMessage(null)} />}
            {renderSection()}
          </div>
        </main>
      </div>
      {isSettingsModalOpen && <SettingsModal isOpen={isSettingsModalOpen} onClose={() => setIsSettingsModalOpen(false)} currentCNPJ={userCompanyCNPJ} onSave={handleSetUserCompanyCNPJ}/>}
      {isProductTemplateModalOpen && (
         <div className="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-50 p-4" role="dialog" aria-modal="true" aria-labelledby="productTemplateModalTitle">
            <div className="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div className="flex justify-between items-center mb-4">
                    <h2 id="productTemplateModalTitle" className="text-xl font-semibold text-gray-800">Instruções do Modelo CSV para Cadastro de Produtos</h2>
                    <button onClick={() => setIsProductTemplateModalOpen(false)} className="text-gray-500 hover:text-gray-700" aria-label="Fechar modal de instruções"><XMarkIcon className="w-6 h-6" /></button>
                </div>
                <p className="mb-4 text-sm text-gray-600">Use um arquivo CSV com as seguintes colunas (separador ponto e vírgula ';'). A primeira linha deve ser o cabeçalho.</p>
                <div className="overflow-x-auto">
                    <table className="min-w-full text-sm divide-y divide-gray-200">
                        <thead className="bg-gray-50"><tr>
                            <th scope="col" className="px-4 py-2 text-left font-medium text-gray-600">Nome da Coluna</th>
                            <th scope="col" className="px-4 py-2 text-left font-medium text-gray-600">Descrição</th>
                            <th scope="col" className="px-4 py-2 text-left font-medium text-gray-600">Exemplo</th>
                            <th scope="col" className="px-4 py-2 text-left font-medium text-gray-600">Obrigatório?</th>
                        </tr></thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {PRODUCT_MASTER_COLUMNS.map(colName => {
                                const details = PRODUCT_MASTER_COLUMN_DESCRIPTIONS[colName];
                                return (<tr key={colName}><td className="px-4 py-2 font-mono text-xs text-indigo-700">{colName}</td><td className="px-4 py-2 text-gray-700">{details.desc}</td><td className="px-4 py-2 font-mono text-xs text-gray-600">{details.example}</td><td className="px-4 py-2 text-center">{details.required ? <span className="text-red-500 font-bold">Sim</span> : 'Não'}</td></tr>);
                            })}
                        </tbody>
                    </table>
                </div>
                <p className="mt-4 text-xs text-gray-500"><strong>Exemplo de cabeçalho CSV:</strong> <br/><code className="block bg-gray-100 p-2 rounded break-all">{PRODUCT_MASTER_COLUMNS.join(';')}</code></p>
            </div>
        </div>
      )}
    </div>
  );
};
export default App;


