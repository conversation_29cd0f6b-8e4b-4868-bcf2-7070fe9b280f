-- Schema para o FiscalIA - Gestor Fiscal Inteligente
-- Execute este script no SQL Editor do Supabase

-- <PERSON>bela de configurações do usuário
CREATE TABLE IF NOT EXISTS user_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    userCompanyCNPJ TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Tabela de produtos
CREATE TABLE IF NOT EXISTS products (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    CODIGO_INTERNO_PRODUTO TEXT NOT NULL,
    DESCRICAO_PRODUTO TEXT,
    NCM TEXT,
    <PERSON>OP TEXT,
    CST_ICMS TEXT,
    CST_PIS TEXT,
    CST_COFINS TEXT,
    ALIQUOTA_ICMS DECIMAL,
    ALIQUOTA_PIS DECIMAL,
    ALIQUOTA_COFINS DECIMAL,
    STATUS_PRODUTO TEXT,
    CATEGORIA_PRODUTO TEXT,
    UNIDADE_MEDIDA TEXT,
    PESO_LIQUIDO DECIMAL,
    PESO_BRUTO DECIMAL,
    VALOR_UNITARIO DECIMAL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, CODIGO_INTERNO_PRODUTO)
);

-- Tabela de notas fiscais
CREATE TABLE IF NOT EXISTS fiscal_notes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    chNFe TEXT NOT NULL,
    fileName TEXT,
    documentType TEXT,
    tipo TEXT, -- 'ENTRADA' ou 'SAIDA'
    nNF TEXT,
    serie TEXT,
    dhEmi TIMESTAMP WITH TIME ZONE,
    dhSaiEnt TIMESTAMP WITH TIME ZONE,
    emitCNPJ TEXT,
    emitCPF TEXT,
    emitNome TEXT,
    destCNPJ TEXT,
    destCPF TEXT,
    destNome TEXT,
    vNF DECIMAL,
    vBC DECIMAL,
    vICMS DECIMAL,
    vBCST DECIMAL,
    vST DECIMAL,
    vProd DECIMAL,
    vFrete DECIMAL,
    vSeg DECIMAL,
    vDesc DECIMAL,
    vII DECIMAL,
    vIPI DECIMAL,
    vPIS DECIMAL,
    vCOFINS DECIMAL,
    vOutro DECIMAL,
    vTotTrib DECIMAL,
    naturezaOperacao TEXT,
    error TEXT,
    itens JSONB, -- Array de itens da nota fiscal
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, chNFe)
);

-- Habilitar RLS nas tabelas
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE fiscal_notes ENABLE ROW LEVEL SECURITY;

-- Políticas RLS para user_settings
CREATE POLICY "Users can view own settings" ON user_settings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own settings" ON user_settings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own settings" ON user_settings
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own settings" ON user_settings
    FOR DELETE USING (auth.uid() = user_id);

-- Políticas RLS para products
CREATE POLICY "Users can view own products" ON products
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own products" ON products
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own products" ON products
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own products" ON products
    FOR DELETE USING (auth.uid() = user_id);

-- Políticas RLS para fiscal_notes
CREATE POLICY "Users can view own fiscal notes" ON fiscal_notes
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own fiscal notes" ON fiscal_notes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own fiscal notes" ON fiscal_notes
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own fiscal notes" ON fiscal_notes
    FOR DELETE USING (auth.uid() = user_id);

-- Índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_products_user_id ON products(user_id);
CREATE INDEX IF NOT EXISTS idx_products_codigo ON products(user_id, CODIGO_INTERNO_PRODUTO);
CREATE INDEX IF NOT EXISTS idx_fiscal_notes_user_id ON fiscal_notes(user_id);
CREATE INDEX IF NOT EXISTS idx_fiscal_notes_chnfe ON fiscal_notes(user_id, chNFe);
CREATE INDEX IF NOT EXISTS idx_fiscal_notes_tipo ON fiscal_notes(user_id, tipo);
CREATE INDEX IF NOT EXISTS idx_fiscal_notes_dhemi ON fiscal_notes(user_id, dhEmi);

-- Função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para atualizar updated_at
CREATE TRIGGER update_user_settings_updated_at BEFORE UPDATE ON user_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_fiscal_notes_updated_at BEFORE UPDATE ON fiscal_notes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
