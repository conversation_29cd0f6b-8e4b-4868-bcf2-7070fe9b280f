
import React, { useState } from 'react';
import { NotaFiscal, ProdutoNotaFiscal } from '../types';
import { DocumentTextIcon, ChevronDownIcon, ChevronUpIcon, CheckCircleIcon, XCircleIcon, QuestionMarkCircleIcon, InformationCircleIcon, MagnifyingGlassIcon } from './Icons';

interface FiscalNoteListProps {
  notes: NotaFiscal[];
  isLoadingAI: boolean;
}

const FiscalNoteItem: React.FC<{ note: NotaFiscal }> = ({ note }) => {
  const [expanded, setExpanded] = useState(false);

  const getStatusIcon = () => {
    if (note.error) return <XCircleIcon className="w-5 h-5 text-red-500" title={note.error} />;
    if (note.tipo === 'ENTRADA') return <CheckCircleIcon className="w-5 h-5 text-blue-500" title="Entrada" />;
    if (note.tipo === 'SAIDA') return <CheckCircleIcon className="w-5 h-5 text-green-500" title="Saída" />;
    return <QuestionMarkCircleIcon className="w-5 h-5 text-yellow-500" title="Tipo Desconhecido/Processando" />;
  };

  return (
    <div className="bg-white shadow rounded-lg mb-3 transition-all duration-300 hover:shadow-md">
      <div 
        className="flex items-center justify-between p-4 cursor-pointer"
        onClick={() => setExpanded(!expanded)}
      >
        <div className="flex items-center space-x-3">
          {getStatusIcon()}
          <DocumentTextIcon className="w-6 h-6 text-gray-500" />
          <div>
            <p className="font-medium text-gray-800 text-sm">{note.fileName}</p>
            <p className="text-xs text-gray-500">
              {note.dhEmi ? new Date(note.dhEmi).toLocaleDateString() : 'Data N/A'} - Valor: R$ {note.vNF || 'N/A'}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
            {note.tipo && <span className={`text-xs font-semibold px-2 py-0.5 rounded-full ${
                note.tipo === 'ENTRADA' ? 'bg-blue-100 text-blue-700' :
                note.tipo === 'SAIDA' ? 'bg-green-100 text-green-700' :
                note.error ? 'bg-red-100 text-red-700' : 'bg-yellow-100 text-yellow-700'
            }`}>
                {note.error ? "ERRO" : note.tipo}
            </span>}
          {expanded ? <ChevronUpIcon className="w-5 h-5 text-gray-500" /> : <ChevronDownIcon className="w-5 h-5 text-gray-500" />}
        </div>
      </div>
      {expanded && (
        <div className="border-t border-gray-200 p-4 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs text-gray-700">
            <div><strong>ID:</strong> {note.id}</div>
            <div><strong>Emitente:</strong> {note.emitName || note.emitCNPJ || note.emitCPF || 'N/A'}</div>
            <div><strong>Destinatário:</strong> {note.destName || note.destCNPJ || note.destCPF || 'N/A'}</div>
            {note.error && <div className="md:col-span-2 text-red-600"><strong>Erro:</strong> {note.error}</div>}
          </div>
          {note.itens.length > 0 && (
            <div className="mt-4">
              <h4 className="text-sm font-semibold text-gray-700 mb-2">Itens da Nota ({note.itens.length}):</h4>
              <div className="max-h-48 overflow-y-auto border border-gray-200 rounded-md">
                <table className="min-w-full text-xs">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="px-2 py-1 text-left">Cód.</th>
                      <th className="px-2 py-1 text-left">Descrição</th>
                      <th className="px-2 py-1 text-right">Qtd.</th>
                      <th className="px-2 py-1 text-right">V.Unit.</th>
                      <th className="px-2 py-1 text-right">V.Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    {note.itens.map((item, index) => (
                      <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                        <td className="px-2 py-1">{item.cProd}</td>
                        <td className="px-2 py-1">{item.xProd}</td>
                        <td className="px-2 py-1 text-right">{item.qCom}</td>
                        <td className="px-2 py-1 text-right">{item.vUnCom}</td>
                        <td className="px-2 py-1 text-right">{item.vProd}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};


const FiscalNoteList: React.FC<FiscalNoteListProps> = ({ notes, isLoadingAI }) => {
  const [searchTerm, setSearchTerm] = useState('');

  if (notes.length === 0) {
    return (
      <div className="text-center py-10 bg-white rounded-lg shadow">
        <DocumentTextIcon className="w-16 h-16 mx-auto text-gray-400 mb-4" />
        <p className="text-gray-500 text-lg">Nenhuma nota fiscal carregada ainda.</p>
        <p className="text-sm text-gray-400 mt-1">Faça o upload dos arquivos XML na seção "Uploads".</p>
      </div>
    );
  }

  const filteredNotes = notes.filter(note => 
    note.fileName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (note.emitCNPJ && note.emitCNPJ.includes(searchTerm)) ||
    (note.destCNPJ && note.destCNPJ.includes(searchTerm)) ||
    (note.vNF && note.vNF.includes(searchTerm))
  );

  return (
    <div className="bg-white shadow-lg rounded-lg overflow-hidden">
      <div className="p-4 border-b border-gray-200 flex flex-col sm:flex-row justify-between items-center">
        <h3 className="text-xl font-semibold text-gray-700 mb-2 sm:mb-0">Notas Fiscais Carregadas</h3>
        <div className="relative">
            <input 
                type="text"
                placeholder="Buscar notas..."
                className="pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
            />
            <MagnifyingGlassIcon className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"/>
        </div>
      </div>
      {isLoadingAI && (
        <div className="p-4 text-sm text-indigo-600 bg-indigo-50 flex items-center">
          <InformationCircleIcon className="w-5 h-5 mr-2" />
          Aguarde, a IA está classificando as notas fiscais...
        </div>
      )}
      <div className="p-4 max-h-[60vh] overflow-y-auto">
        {filteredNotes.length > 0 ? (
            filteredNotes.map(note => <FiscalNoteItem key={note.id} note={note} />)
        ) : (
            <p className="text-gray-500">Nenhuma nota fiscal encontrada com o termo "{searchTerm}".</p>
        )}
      </div>
       {notes.length > 0 && (
        <div className="p-4 border-t border-gray-200 text-sm text-gray-500">
          Total de notas: {notes.length}. Mostrando: {filteredNotes.length}.
        </div>
      )}
    </div>
  );
};

export default FiscalNoteList;
