
import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { UploadIcon } from './Icons';

interface FileUploadProps {
  onFileSelect?: (file: File) => void;
  onFilesSelect?: (files: FileList) => void;
  acceptedFileTypes: string; // e.g., ".csv, .xml, image/*"
  buttonText: string;
  label?: string;
  multiple?: boolean;
}

const FileUpload: React.FC<FileUploadProps> = ({ onFileSelect, onFilesSelect, acceptedFileTypes, buttonText, label, multiple = false }) => {
  const [dragOver, setDragOver] = useState(false);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setDragOver(false);
    if (multiple && onFilesSelect && acceptedFiles.length > 0) {
        // Create a FileList from acceptedFiles
        const dataTransfer = new DataTransfer();
        acceptedFiles.forEach(file => dataTransfer.items.add(file));
        onFilesSelect(dataTransfer.files);
    } else if (!multiple && onFileSelect && acceptedFiles.length > 0) {
      onFileSelect(acceptedFiles[0]);
    }
  }, [onFileSelect, onFilesSelect, multiple]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedFileTypes.split(',').reduce((acc, type) => {
        const trimmedType = type.trim();
        if (trimmedType) { // @ts-ignore // This is a valid structure for react-dropzone
            acc[trimmedType] = [];
        }
        return acc;
    }, {}),
    multiple,
    onDragEnter: () => setDragOver(true),
    onDragLeave: () => setDragOver(false),
  });

  return (
    <div
      {...getRootProps()}
      className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
                  ${isDragActive || dragOver ? 'border-indigo-500 bg-indigo-50' : 'border-gray-300 hover:border-gray-400 bg-white'}`}
    >
      <input {...getInputProps()} />
      <div className="flex flex-col items-center justify-center">
        <UploadIcon className="w-12 h-12 text-gray-400 mb-3" />
        {label && <p className="mb-2 text-sm text-gray-600">{label}</p>}
        <button
          type="button"
          className="px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors text-sm font-medium"
        >
          {buttonText}
        </button>
        <p className="mt-2 text-xs text-gray-500">Tipos aceitos: {acceptedFileTypes}</p>
      </div>
    </div>
  );
};

export default FileUpload;
