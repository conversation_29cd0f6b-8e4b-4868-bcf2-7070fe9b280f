
import { GoogleGenAI, GenerateContentResponse } from "@google/genai";
import { NotaFiscal, Discrepancy, ReportData } from '../types';
import { GEMINI_TEXT_MODEL } from '../constants';
// Attempt to import from config as a fallback or primary source
import { GEMINI_API_KEY as GEMINI_API_KEY_CONFIG } from '../src/config'; // Corrected path

const API_KEY = process.env.API_KEY || GEMINI_API_KEY_CONFIG;

if (!API_KEY) {
  const errorMessage = "API_KEY do Gemini NÃO CONFIGURADA. As funcionalidades de IA NÃO FUNCIONARÃO. " +
                       "Defina process.env.API_KEY ou GEMINI_API_KEY em src/config.ts.";
  console.error(errorMessage);
  alert(errorMessage); // Make it more visible to the developer during local dev
}

const ai = new GoogleGenAI({ apiKey: API_KEY! }); // The '!' asserts API_KEY is non-null, errors handled above

export const classifyNoteTypeWithAI = async (
  note: NotaFiscal,
  userCompanyCNPJ: string
): Promise<'ENTRADA' | 'SAIDA' | 'DESCONHECIDO'> => {
  if (!API_KEY) { 
    console.warn("Classificação de IA pulada: API Key não encontrada.");
    return 'DESCONHECIDO';
  }
  if (!userCompanyCNPJ) {
    console.warn("Classificação de IA pulada: CNPJ do usuário não fornecido.");
    return 'DESCONHECIDO';
  }

  const { emitCNPJ, emitCPF, destCNPJ, destCPF, documentType } = note;

  // Rule-based classification first (token-free)
  if (emitCNPJ === userCompanyCNPJ || emitCPF === userCompanyCNPJ) return 'SAIDA';
  if (destCNPJ === userCompanyCNPJ || destCPF === userCompanyCNPJ) return 'ENTRADA';
  
  const prompt = `
    Contexto: Análise de nota fiscal para a empresa com CNPJ ${userCompanyCNPJ}.
    Dados da Nota Fiscal:
    - Tipo de Documento: ${documentType || 'Não especificado'}
    - CNPJ/CPF do Emitente: ${emitCNPJ || emitCPF || 'Não informado'}
    - CNPJ/CPF do Destinatário: ${destCNPJ || destCPF || 'Não informado'}
    - Nome do Arquivo: ${note.fileName}
    - Natureza da Operação (se houver): ${note.naturezaOperacao || 'Não informada'}

    Tarefa: Classifique esta nota como 'ENTRADA' ou 'SAIDA' para a empresa ${userCompanyCNPJ}.
    Responda APENAS com 'ENTRADA', 'SAIDA', ou 'DESCONHECIDO'. Não adicione nenhuma explicação.
  `;

  try {
    const response: GenerateContentResponse = await ai.models.generateContent({
        model: GEMINI_TEXT_MODEL,
        contents: prompt,
    });
    const textResponse = response.text.trim().toUpperCase();

    if (textResponse === 'ENTRADA' || textResponse === 'SAIDA') {
      return textResponse as 'ENTRADA' | 'SAIDA';
    }
    console.warn(`Resposta inesperada da IA para classificação: ${textResponse}. Nota: ${note.fileName}`);
    return 'DESCONHECIDO';
  } catch (error) {
    console.error("Erro ao classificar nota com Gemini:", error);
    return 'DESCONHECIDO';
  }
};

export const getAIChatResponse = async (userQuery: string, contextData?: string): Promise<string> => {
  if (!API_KEY) return "Desculpe, a funcionalidade de IA não está disponível (sem chave de API).";
  
  let prompt = `Você é um assistente fiscal inteligente para usuários brasileiros.
                Responda à pergunta do usuário de forma detalhada, útil e em português.
                Seja amigável e profissional.
                Sempre que possível, utilize listas, exemplos práticos e explique o raciocínio fiscal.
                Ao final da resposta, sugira de 2 a 3 perguntas ou comandos úteis que o usuário pode fazer a seguir, sob o título "Sugestões de perguntas:".
                Separe claramente a resposta principal das sugestões, usando uma linha em branco entre elas.`;

  if (contextData && contextData.length > 10) {
    prompt += `\n\nUse o seguinte CONTEXTO DE DADOS (amostra dos dados do usuário) para basear sua resposta, se relevante:
               ${contextData}
               Se o contexto não for suficiente ou não relacionado à pergunta, informe que precisa de mais detalhes ou que a pergunta está fora do escopo dos dados fornecidos.
               NÃO invente informações que não estão no contexto.`;
  }

  prompt += `\n\nPERGUNTA DO USUÁRIO: "${userQuery}"
             \nRESPOSTA DETALHADA:`;
  
  try {
    const response: GenerateContentResponse = await ai.models.generateContent({
        model: GEMINI_TEXT_MODEL,
        contents: prompt,
        config: {
            temperature: 0.5, 
            topK: 30,
            topP: 0.9,
        }
    });
    return response.text.trim();
  } catch (error) {
    console.error("Erro ao obter resposta do chat com Gemini:", error);
    if (error instanceof Error && error.message.toLowerCase().includes("api key not valid")) {
        return "Erro: Chave de API inválida. Verifique as configurações.";
    }
    return "Erro: Falha ao comunicar com a IA. Tente novamente mais tarde.";
  }
};

export const getFiscalMonitoringSummaryAI = async (discrepancies: Discrepancy[]): Promise<string> => {
    if (!API_KEY) return "Funcionalidade de resumo de IA indisponível (sem chave de API).";
    if (discrepancies.length === 0) return "Nenhuma divergência fiscal encontrada para resumir.";

    const sampleSize = Math.min(discrepancies.length, 5);
    const discrepanciesSample = discrepancies.slice(0, sampleSize).map(d => 
        `- Nota ${d.noteFileName}, Item ${d.itemDescription.substring(0,30)}...: Campo '${d.field}' (${d.valueInNote}) diverge do cadastro (${d.valueInMaster}). ${d.message.substring(0,50)}...`
    ).join("\n");

    const prompt = `
        Você é um analista fiscal. As seguintes divergências foram encontradas entre notas fiscais e o cadastro de produtos:
        Total de Divergências Encontradas: ${discrepancies.length}
        Amostra de Divergências:
        ${discrepanciesSample}

        Tarefa: Gere um resumo conciso (2-3 frases) sobre estas divergências, destacando os tipos mais comuns se possível.
        Sugira uma ação geral que o usuário pode tomar. Exemplo: "Foram encontradas X divergências, principalmente em NCMs. Recomenda-se revisar os cadastros e as notas apontadas."
        Responda em português.
    `;
    try {
        const response = await ai.models.generateContent({ model: GEMINI_TEXT_MODEL, contents: prompt });
        return response.text.trim();
    } catch (error) {
        console.error("Erro ao gerar resumo de monitoramento fiscal com IA:", error);
        return "Falha ao gerar resumo das divergências com IA.";
    }
};

export const analyzePotentialCreditsAI = async (processedTaxData: any): Promise<string> => {
    if (!API_KEY) return "Funcionalidade de análise de créditos indisponível (sem chave de API).";
    
    const contextString = JSON.stringify(processedTaxData);

    const prompt = `
        Você é um consultor tributário. Analise os seguintes dados sumarizados de impostos de entrada de uma empresa:
        ${contextString}

        Tarefa: Com base APENAS nos dados fornecidos, identifique de forma genérica possíveis áreas onde créditos fiscais (especialmente PIS/COFINS) podem existir.
        NÃO calcule valores. NÃO garanta créditos.
        Aponte cenários comuns, como aquisição de insumos, bens para o ativo imobilizado, ou despesas que geram crédito.
        Se os dados forem insuficientes, mencione isso.
        Responda em português, em 2-4 frases.
        Exemplo: "Com base nos totais de PIS/COFINS sobre entradas, pode haver potencial para créditos. Verifique se as aquisições se enquadram como insumos ou despesas permitidas pela legislação. A análise detalhada dos itens e NCMs é necessária."
    `;
     try {
        const response = await ai.models.generateContent({ model: GEMINI_TEXT_MODEL, contents: prompt, config: {temperature: 0.3} });
        return response.text.trim();
    } catch (error) {
        console.error("Erro ao analisar créditos potenciais com IA:", error);
        return "Falha ao analisar créditos potenciais com IA.";
    }
};

export const getReportSummaryAI = async (reportData: ReportData, reportTitle: string): Promise<string> => {
    if (!API_KEY) return "Funcionalidade de resumo de relatório indisponível (sem chave de API).";

    let dataSample = "";
    if (reportData.data && reportData.data.length > 0) {
        const sample = reportData.data.slice(0, 3).map(item => JSON.stringify(item)).join("\n");
        dataSample = `Amostra dos dados do relatório:\n${sample}\n`;
    }
    
    const summaryString = reportData.summary ? `Resumo dos totais: ${JSON.stringify(reportData.summary)}\n` : "";

    const prompt = `
        Você é um analista de negócios. O seguinte relatório foi gerado:
        Título do Relatório: ${reportTitle}
        ${summaryString}
        ${dataSample}
        Tarefa: Gere um resumo narrativo conciso (2-4 frases) dos principais pontos deste relatório.
        Destaque os achados mais significativos ou tendências implícitas nos dados fornecidos (resumo e amostra).
        Responda em português.
    `;
    try {
        const response = await ai.models.generateContent({ model: GEMINI_TEXT_MODEL, contents: prompt, config: {temperature: 0.6} });
        return response.text.trim();
    } catch (error) {
        console.error(`Erro ao gerar resumo do relatório '${reportTitle}' com IA:`, error);
        return `Falha ao gerar resumo do relatório '${reportTitle}' com IA.`;
    }
};


export const getProactiveInsightsAI = async (summaryData: any): Promise<string> => {
    if (!API_KEY) return "Funcionalidade de insights proativos indisponível (sem chave de API).";
    
    const contextString = JSON.stringify(summaryData);

    const prompt = `
        Você é um consultor de negócios experiente. Analise o seguinte resumo de dados fiscais e de vendas de uma empresa:
        ${contextString}

        Tarefa: Com base APENAS neste resumo, gere 2-3 insights ou observações proativas que poderiam ser úteis para o gestor.
        Os insights devem ser acionáveis ou levar à reflexão.
        Exemplos: "O produto X representa uma grande parte das vendas, considere estratégias para diversificar." ou "O total de impostos Y parece alto em relação às vendas, pode ser útil revisar o enquadramento tributário ou buscar otimizações."
        NÃO faça previsões definitivas. Use linguagem cautelosa.
        Responda em português, em formato de lista curta.
    `;
    try {
        const response = await ai.models.generateContent({ model: GEMINI_TEXT_MODEL, contents: prompt, config: {temperature: 0.7} });
        return response.text.trim();
    } catch (error) {
        console.error("Erro ao gerar insights proativos com IA:", error);
        return "Falha ao gerar insights proativos com IA.";
    }
};
