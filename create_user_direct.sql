-- Script para criar usuário de teste diretamente
-- Execute no SQL Editor do Supabase

-- Criar usuário diretamente na tabela auth.users
INSERT INTO auth.users (
    instance_id,
    id,
    aud,
    role,
    email,
    encrypted_password,
    email_confirmed_at,
    raw_app_meta_data,
    raw_user_meta_data,
    created_at,
    updated_at,
    confirmation_token,
    email_change,
    email_change_token_new,
    recovery_token
) VALUES (
    '00000000-0000-0000-0000-000000000000',
    gen_random_uuid(),
    'authenticated',
    'authenticated',
    '<EMAIL>',
    crypt('123456', gen_salt('bf')),
    NOW(),
    '{"provider":"email","providers":["email"]}',
    '{}',
    NOW(),
    NOW(),
    '',
    '',
    '',
    ''
) ON CONFLICT (email) DO UPDATE SET
    encrypted_password = EXCLUDED.encrypted_password,
    email_confirmed_at = NOW(),
    updated_at = NOW();

-- Verificar se o usuário foi criado
SELECT 
    id,
    email, 
    email_confirmed_at,
    created_at,
    role
FROM auth.users 
WHERE email = '<EMAIL>';

-- Criar configurações para o usuário
INSERT INTO user_settings (
    user_id,
    userCompanyCNPJ
) 
SELECT 
    id,
    '12.345.678/0001-90'
FROM auth.users 
WHERE email = '<EMAIL>'
ON CONFLICT (user_id) DO NOTHING;
