import { supabase } from './supabaseClient';
import { ProdutoMaster, NotaFiscal } from '../types';

export interface UserSettings {
  id: string;
  user_id: string;
  userCompanyCNPJ: string;
  created_at: string;
  updated_at: string;
}

class SupabaseService {
  // Configurações do usuário
  async getUserSettings(userId: string): Promise<UserSettings | null> {
    try {
      const { data, error } = await supabase
        .from('user_settings')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Erro ao buscar configurações do usuário:', error);
      throw error;
    }
  }

  async saveUserSettings(userId: string, settings: Partial<UserSettings>): Promise<UserSettings> {
    try {
      const { data, error } = await supabase
        .from('user_settings')
        .upsert({
          user_id: userId,
          ...settings,
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Erro ao salvar configurações do usuário:', error);
      throw error;
    }
  }

  // Produtos
  async getProducts(): Promise<{ data: ProdutoMaster[] | null; error: any }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Usuário não autenticado');

      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      return { data, error };
    } catch (error) {
      console.error('Erro ao buscar produtos:', error);
      return { data: null, error };
    }
  }

  async saveProducts(products: ProdutoMaster[]): Promise<{ data: any; error: any }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Usuário não autenticado');

      // Adiciona user_id e timestamps aos produtos
      const productsWithMetadata = products.map(product => ({
        ...product,
        user_id: user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

      const { data, error } = await supabase
        .from('products')
        .upsert(productsWithMetadata, { 
          onConflict: 'user_id,CODIGO_INTERNO_PRODUTO',
          ignoreDuplicates: false 
        })
        .select();

      return { data, error };
    } catch (error) {
      console.error('Erro ao salvar produtos:', error);
      return { data: null, error };
    }
  }

  // Notas Fiscais
  async getFiscalNotes(): Promise<{ data: NotaFiscal[] | null; error: any }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Usuário não autenticado');

      const { data, error } = await supabase
        .from('fiscal_notes')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      return { data, error };
    } catch (error) {
      console.error('Erro ao buscar notas fiscais:', error);
      return { data: null, error };
    }
  }

  async saveFiscalNotes(notes: NotaFiscal[]): Promise<{ data: any; error: any }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Usuário não autenticado');

      // Adiciona user_id e timestamps às notas
      const notesWithMetadata = notes.map(note => ({
        ...note,
        user_id: user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

      const { data, error } = await supabase
        .from('fiscal_notes')
        .upsert(notesWithMetadata, { 
          onConflict: 'user_id,chNFe',
          ignoreDuplicates: false 
        })
        .select();

      return { data, error };
    } catch (error) {
      console.error('Erro ao salvar notas fiscais:', error);
      return { data: null, error };
    }
  }

  // Limpar dados do usuário
  async clearUserData(): Promise<{ success: boolean; error?: any }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Usuário não autenticado');

      // Remove produtos
      const { error: productsError } = await supabase
        .from('products')
        .delete()
        .eq('user_id', user.id);

      if (productsError) throw productsError;

      // Remove notas fiscais
      const { error: notesError } = await supabase
        .from('fiscal_notes')
        .delete()
        .eq('user_id', user.id);

      if (notesError) throw notesError;

      return { success: true };
    } catch (error) {
      console.error('Erro ao limpar dados do usuário:', error);
      return { success: false, error };
    }
  }
}

const supabaseService = new SupabaseService();
export default supabaseService;
