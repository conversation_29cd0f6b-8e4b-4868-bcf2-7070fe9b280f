
export interface ProdutoMaster {
  CODIGO_INTERNO_PRODUTO: string;
  DESCRICAO_PRODUTO: string;
  EAN_GTIN?: string;
  NCM: string;
  CEST?: string;
  ORIGEM_MERCADORIA: string;
  UNIDADE_MEDIDA_COMERCIAL: string;
  ALIQUOTA_ICMS_PADRAO?: string; 
  CST_CSOSN_ICMS_PADRAO?: string;
  ALIQUOTA_IPI_PADRAO?: string;
  CST_IPI_PADRAO?: string;
  ALIQUOTA_PIS_PADRAO?: string;
  CST_PIS_PADRAO?: string;
  ALIQUOTA_COFINS_PADRAO?: string;
  CST_COFINS_PADRAO?: string;
  REGIME_TRIBUTARIO_PRODUTO?: string;
  CATEGORIA_PRODUTO?: string;
  MARCA_PRODUTO?: string;
  FORNECEDOR_PRINCIPAL_CNPJ_CPF?: string;
  PRECO_CUSTO_ULTIMA_ENTRADA?: string;
  PRECO_VENDA_PADRAO?: string;
  STATUS_PRODUTO?: string;
  DATA_ULTIMA_ATUALIZACAO_CADASTRO?: string;
}

export interface TaxDetail {
  vBC?: string; // Valor da Base de Cálculo
  p?: string;   // Alíquota (percentual)
  v?: string;   // Valor do Imposto
}

export interface ProdutoNotaFiscal {
  cProd: string; 
  xProd: string; 
  NCM: string;   
  qCom: string;  
  uCom: string; 
  vUnCom: string; 
  vProd: string;  
  // Detailed taxes per item
  ICMS?: TaxDetail & { CST?: string; CSOSN?: string; orig?: string; modBC?: string; vICMSDeson?: string; motDesICMS?: string; };
  ICMSST?: TaxDetail & { vBCSTRet?: string; pST?: string; vICMSSTRet?: string; vBCSTDest?: string; vICMSSTDest?: string;}; // For ST details
  IPI?: TaxDetail & { cEnq?: string; CST?: string; };
  PIS?: TaxDetail & { CST?: string; };
  PISST?: TaxDetail;
  COFINS?: TaxDetail & { CST?: string; };
  COFINSST?: TaxDetail;
  ISSQN?: TaxDetail & { cListServ?: string; cSitTrib?: string; };
}

export type DocumentType = 'NFe' | 'NFCe' | 'NFSe' | 'CTe' | 'UNKNOWN';

export interface NotaFiscal {
  id: string; 
  fileName: string;
  documentType: DocumentType;
  dhEmi?: string; 
  emitCNPJ?: string;
  emitCPF?: string;
  emitName?: string;
  destCNPJ?: string;
  destCPF?: string;
  destName?: string;
  vNF?: string; 
  vTotTrib?: string; // Valor total de tributos (often in infAdic or a specific tag)
  itens: ProdutoNotaFiscal[];
  tipo?: 'ENTRADA' | 'SAIDA' | 'DESCONHECIDO'; 
  naturezaOperacao?: string; 
  xmlContent: string; 
  error?: string; 
}

export interface ChatMessage {
  id: string;
  sender: 'user' | 'ai';
  text: string;
  timestamp: Date;
}

export enum AppSection {
  Dashboard = 'Dashboard',
  Uploads = 'Uploads',
  Products = 'Produtos',
  FiscalNotes = 'Notas Fiscais', 
  Relatorios = 'Relatórios',
  AIChat = 'Chat IA',
  Settings = 'Configurações',
}

// For Monitoramento Fiscal
export interface Discrepancy {
  noteId: string;
  noteFileName: string;
  itemCode: string;
  itemDescription: string;
  field: string; // e.g., NCM, CST_ICMS, ALIQ_ICMS
  valueInNote: string | undefined;
  valueInMaster: string | undefined;
  message: string;
}

export interface ReportData {
  title: string;
  data: any[];
  columns?: { key: string; label: string }[];
  summary?: Record<string, string | number>;
}

export interface SalesEvolutionDataPoint {
  period: string; // e.g., "YYYY-MM"
  totalSales: number;
}
