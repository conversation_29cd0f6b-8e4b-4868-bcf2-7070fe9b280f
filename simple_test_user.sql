-- Script SIMPLES para criar usuário de teste
-- Execute no SQL Editor do Supabase

-- Desabilitar confirmação de email temporariamente
UPDATE auth.config SET email_confirm = false;

-- Criar usu<PERSON><PERSON> diretamente (mé<PERSON><PERSON> mais direto)
INSERT INTO auth.users (
    id,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    aud,
    role
) VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    crypt('123456', gen_salt('bf')),
    NOW(),
    NOW(),
    NOW(),
    'authenticated',
    'authenticated'
) ON CONFLICT (email) DO UPDATE SET
    encrypted_password = EXCLUDED.encrypted_password,
    email_confirmed_at = NOW();

-- Verificar se funcionou
SELECT email, created_at, email_confirmed_at FROM auth.users WHERE email = '<EMAIL>';
