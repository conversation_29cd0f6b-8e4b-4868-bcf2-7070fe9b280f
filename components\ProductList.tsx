
import React from 'react';
import { ProdutoMaster } from '../types';
import { PRODUCT_MASTER_COLUMNS } from '../constants';
import { CubeIcon, InfoIcon } from './Icons';


interface ProductListProps {
  products: ProdutoMaster[];
}

const ProductList: React.FC<ProductListProps> = ({ products }) => {
  if (products.length === 0) {
    return (
      <div className="text-center py-10 bg-white rounded-lg shadow">
        <CubeIcon className="w-16 h-16 mx-auto text-gray-400 mb-4" />
        <p className="text-gray-500 text-lg">Nenhum produto cadastrado ainda.</p>
        <p className="text-sm text-gray-400 mt-1">Faça o upload do arquivo CSV de produtos na seção "Uploads".</p>
      </div>
    );
  }

  return (
    <div className="bg-white shadow-lg rounded-lg overflow-hidden">
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-xl font-semibold text-gray-700">Lista de Produtos Cadastrados</h3>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {PRODUCT_MASTER_COLUMNS.slice(0, 6).map((colName) => ( // Show first 6 columns for brevity
                <th
                  key={colName}
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  {colName.replace(/_/g, ' ')}
                </th>
              ))}
               <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Detalhes
               </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {products.map((product, index) => (
              <tr key={product.CODIGO_INTERNO_PRODUTO || index} className="hover:bg-gray-50 transition-colors">
                {PRODUCT_MASTER_COLUMNS.slice(0, 6).map((colName) => (
                  <td key={colName} className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                    {String(product[colName] === undefined || product[colName] === null ? '-' : product[colName])}
                  </td>
                ))}
                 <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                    <button title="Ver todos os detalhes (implementação futura)" className="text-indigo-600 hover:text-indigo-800">
                        <InfoIcon className="w-5 h-5"/>
                    </button>
                 </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {products.length > 10 && (
        <div className="p-4 border-t border-gray-200 text-sm text-gray-500">
          Mostrando os primeiros {Math.min(products.length, 10)} de {products.length} produtos. (Paginação futura)
        </div>
      )}
    </div>
  );
};

export default ProductList;
