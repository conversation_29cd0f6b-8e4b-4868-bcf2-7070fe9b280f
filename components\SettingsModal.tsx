
import React, { useState, useEffect } from 'react';
import { XMarkIcon, CheckIcon } from './Icons';

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentCNPJ: string;
  onSave: (cnpj: string) => void;
}

const SettingsModal: React.FC<SettingsModalProps> = ({ isOpen, onClose, currentCNPJ, onSave }) => {
  const [cnpj, setCnpj] = useState(currentCNPJ);

  useEffect(() => {
    setCnpj(currentCNPJ);
  }, [currentCNPJ, isOpen]);

  if (!isOpen) return null;

  const handleSave = () => {
    onSave(cnpj.replace(/\D/g, '')); // Remove non-digits
    onClose();
  };
  
  const formatCNPJ = (value: string) => {
    const cleaned = value.replace(/\D/g, '');
    if (cleaned.length <= 2) return cleaned;
    if (cleaned.length <= 5) return `${cleaned.slice(0,2)}.${cleaned.slice(2)}`;
    if (cleaned.length <= 8) return `${cleaned.slice(0,2)}.${cleaned.slice(2,5)}.${cleaned.slice(5)}`;
    if (cleaned.length <= 12) return `${cleaned.slice(0,2)}.${cleaned.slice(2,5)}.${cleaned.slice(5,8)}/${cleaned.slice(8)}`;
    return `${cleaned.slice(0,2)}.${cleaned.slice(2,5)}.${cleaned.slice(5,8)}/${cleaned.slice(8,12)}-${cleaned.slice(12,14)}`;
  };


  return (
    <div className="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-white p-8 rounded-lg shadow-xl max-w-md w-full">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold text-gray-800">Configurações</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <XMarkIcon className="w-7 h-7" />
          </button>
        </div>
        
        <div className="space-y-4">
          <div>
            <label htmlFor="companyCNPJ" className="block text-sm font-medium text-gray-700 mb-1">
              CNPJ da sua Empresa
            </label>
            <input
              type="text"
              id="companyCNPJ"
              value={formatCNPJ(cnpj)}
              onChange={(e) => setCnpj(e.target.value)}
              placeholder="00.000.000/0000-00"
              maxLength={18}
              className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-shadow"
            />
            <p className="mt-1 text-xs text-gray-500">
              Este CNPJ será usado pela IA para classificar notas fiscais como Entrada ou Saída.
            </p>
          </div>
        </div>

        <div className="mt-8 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400"
          >
            Cancelar
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 flex items-center"
          >
            <CheckIcon className="w-5 h-5 mr-1.5" />
            Salvar
          </button>
        </div>
      </div>
    </div>
  );
};

export default SettingsModal;
