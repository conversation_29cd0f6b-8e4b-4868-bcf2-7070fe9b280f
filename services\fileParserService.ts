
import { ProdutoMaster } from '../types';
import { PRODUCT_MASTER_COLUMNS, PRODUCT_MASTER_COLUMN_DESCRIPTIONS } from '../constants';

export const parseProductCSV = (file: File): Promise<ProdutoMaster[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const csvText = event.target?.result as string;
        const lines = csvText.split(/\r\n|\n/).filter(line => line.trim() !== '');
        if (lines.length < 2) {
          throw new Error("CSV inválido. Deve conter cabeçalho e pelo menos uma linha de dados.");
        }

        const headerLine = lines[0].trim();
        const headers = headerLine.split(';').map(h => h.trim() as keyof ProdutoMaster);
        
        const requiredColumnHeaders = PRODUCT_MASTER_COLUMNS.filter(colKey => 
            PRODUCT_MASTER_COLUMN_DESCRIPTIONS[colKey]?.required
        );

        const missingRequiredHeaders = requiredColumnHeaders.filter(rh => !headers.includes(rh));

        if (missingRequiredHeaders.length > 0) {
           throw new Error(`Cabeçalhos obrigatórios ausentes no CSV: ${missingRequiredHeaders.join(', ')}. Certifique-se de que o separador é ';' e todos os campos marcados como "Sim" na tabela de modelo estejam presentes.`);
        }
        
        const products: ProdutoMaster[] = [];
        for (let i = 1; i < lines.length; i++) {
          const values = lines[i].split(';');
          const product = {} as ProdutoMaster;
          let missingRequiredValueInRow = false;
          headers.forEach((header, index) => {
            // Check if the header from CSV is a known column in ProdutoMaster
            if (PRODUCT_MASTER_COLUMNS.includes(header)) {
              const value = values[index]?.trim() || undefined;
              // @ts-ignore
              product[header] = value;
              if (PRODUCT_MASTER_COLUMN_DESCRIPTIONS[header]?.required && !value) {
                missingRequiredValueInRow = true;
                console.warn(`Produto na linha ${i+1} ignorado: Campo obrigatório '${header}' está vazio.`);
              }
            }
          });

          if (missingRequiredValueInRow) {
            continue; // Skip this product due to missing required field value
          }
          
          // Final check on key identifiers after attempting to map all values
          if (!product.CODIGO_INTERNO_PRODUTO || !product.DESCRICAO_PRODUTO || !product.NCM) {
            console.warn(`Produto na linha ${i+1} ignorado por falta de campos chave (Código Interno, Descrição ou NCM) mesmo após mapeamento.`);
            continue;
          }
          products.push(product);
        }
        resolve(products);
      } catch (error) {
        console.error("Erro ao parsear CSV de produtos:", error);
        reject(new Error(`Erro ao processar o arquivo CSV: ${(error as Error).message}`));
      }
    };
    reader.onerror = (error) => {
      console.error("Erro ao ler arquivo:", error);
      reject(new Error("Não foi possível ler o arquivo."));
    };
    reader.readAsText(file);
  });
};
