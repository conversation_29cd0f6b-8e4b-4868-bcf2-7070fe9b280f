-- <PERSON>ript para criar um usuário genérico de teste no Supabase
-- Execute este script no SQL Editor do Supabase

-- MÉTODO MAIS SIMPLES: Usar a função do Supabase para criar usuário

-- 1. <PERSON><PERSON>, vamos desabilitar temporariamente a confirmação de email
UPDATE auth.config
SET email_confirm = false
WHERE id = 1;

-- 2. <PERSON><PERSON>r usuário usando a API interna do Supabase
-- Este comando simula o registro via API
SELECT auth.signup(
    '<EMAIL>',  -- email
    '123456',              -- senha
    '{"full_name": "Admin FiscalIA"}'::jsonb  -- metadados
);

-- 3. Confirmar o email automaticamente (caso necessário)
UPDATE auth.users
SET email_confirmed_at = NOW()
WHERE email = '<EMAIL>'
AND email_confirmed_at IS NULL;

-- 4. Inserir configurações iniciais para o usuário
INSERT INTO user_settings (
    user_id,
    userCompanyCNPJ,
    created_at,
    updated_at
)
SELECT
    id,
    '12.345.678/0001-90', -- CNPJ de exemplo
    NOW(),
    NOW()
FROM auth.users
WHERE email = '<EMAIL>'
ON CONFLICT (user_id) DO NOTHING;

-- 5. Verificar se o usuário foi criado com sucesso
SELECT
    u.id,
    u.email,
    u.email_confirmed_at,
    u.created_at,
    us.userCompanyCNPJ
FROM auth.users u
LEFT JOIN user_settings us ON u.id = us.user_id
WHERE u.email = '<EMAIL>';
