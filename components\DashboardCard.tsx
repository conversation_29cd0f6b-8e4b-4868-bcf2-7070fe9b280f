import React from 'react';
import { ArrowTrendingUpIcon, ArrowTrendingDownIcon } from './Icons';

interface DashboardCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: {
    direction: 'up' | 'down' | 'neutral';
    text: string;
    IconComponent?: React.FC<{className?: string; title?: string}>; // Optional specific trend icon
  };
  iconContainerClass?: string; // To style the main icon if needed
}

const DashboardCard: React.FC<DashboardCardProps> = ({ title, value, icon, trend, iconContainerClass }) => {
  const TrendIcon = trend?.direction === 'up' ? ArrowTrendingUpIcon : trend?.direction === 'down' ? ArrowTrendingDownIcon : null;
  
  let trendColorClass = 'text-gray-500';
  if (trend?.direction === 'up') trendColorClass = 'text-green-500';
  if (trend?.direction === 'down') trendColorClass = 'text-red-500';

  return (
    <div className="bg-white p-5 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 flex flex-col justify-between min-h-[130px]">
      <div className="flex justify-between items-start">
        <div>
          <h4 className="text-xs font-semibold text-gray-400 uppercase tracking-wider">{title}</h4>
          <p className="text-2xl md:text-3xl font-bold text-gray-800 mt-1">{value}</p>
        </div>
        <div className={`p-1 ${iconContainerClass || ''}`}>
          {icon}
        </div>
      </div>
      {trend && (
        <div className={`flex items-center text-xs mt-2 ${trendColorClass}`}>
          {trend.IconComponent ? <trend.IconComponent className="w-4 h-4 mr-1" /> : TrendIcon && <TrendIcon className="w-4 h-4 mr-1" />}
          <span>{trend.text}</span>
        </div>
      )}
    </div>
  );
};

export default DashboardCard;
